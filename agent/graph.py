"""Simplified LangGraph implementation for the instruction processing agent."""

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolN<PERSON>, tools_condition

from agent.state import AgentState, create_initial_state
from agent.nodes import (
    planning_node,
    task_node,
    format_response_node,
    process_tool_results_node
)
from agent.tools import TOOLS


def should_continue_after_planning(state: AgentState) -> str:
    """
    Conditional edge function after planning to determine next step.

    Args:
        state: Current agent state

    Returns:
        Next node name
    """
    # Check for errors
    if state.get("error"):
        return "format_response"

    # Check if plan was created successfully
    plan = state.get("plan")
    if not plan or not plan.tasks:
        return "format_response"

    # Check if clarification is needed
    if plan.clarification_needed:
        return "format_response"  # Return with clarification questions

    # Start task execution
    return "task_node"


def should_continue_after_task(state: AgentState) -> str:
    """
    Conditional edge function after task execution.

    Args:
        state: Current agent state

    Returns:
        Next node name
    """
    # Check for errors
    if state.get("error"):
        return "format_response"

    # Check if we've reached the maximum number of steps
    current_step = state.get("current_step", 0)
    max_steps = state.get("max_steps", 5)

    if current_step >= max_steps:
        return "format_response"

    # Check if more tasks need to be executed
    needs_action = state.get("needs_action", False)

    if needs_action:
        return "task_node"  # Continue with next task
    else:
        return "format_response"  # All tasks completed


def should_continue_loop(state: AgentState) -> str:
    """
    Conditional edge function to determine if the processing loop should continue.

    Args:
        state: Current agent state

    Returns:
        Next node name or END
    """
    # Check for errors
    if state.get("error"):
        return "format_response"

    # Check if we've reached the maximum number of steps
    current_step = state.get("current_step", 0)
    max_steps = state.get("max_steps", 5)

    if current_step >= max_steps:
        return "format_response"

    # Check if analysis indicates we need to take action
    needs_action = state.get("needs_action", False)

    if needs_action:
        return "execute_action"
    else:
        return "format_response"


def should_continue_after_action(state: AgentState) -> str:
    """
    Conditional edge function after action execution.

    Args:
        state: Current agent state

    Returns:
        Next node name or END
    """
    # Check for errors
    if state.get("error"):
        return "format_response"

    # Check if we've reached the maximum number of steps
    current_step = state.get("current_step", 0)
    max_steps = state.get("max_steps", 5)

    if current_step >= max_steps:
        return "format_response"

    # Check if we have action results - if so, we're likely done
    action_results = state.get("action_results", [])
    if action_results:
        # Only continue if we haven't taken too many steps
        if current_step < 2:  # Allow max 2 analysis-action cycles
            return "analyze_messages"
        else:
            return "format_response"

    # Continue the loop to analyze if more actions are needed
    return "analyze_messages"


def create_agent_graph():
    """
    Create and configure the planning-based LangGraph agent.

    Returns:
        Compiled LangGraph agent
    """
    # Create the state graph
    workflow = StateGraph(AgentState)

    # Add nodes for the planning-based workflow
    workflow.add_node("planning", planning_node)
    workflow.add_node("task_node", task_node)
    workflow.add_node("tools", ToolNode(TOOLS))
    workflow.add_node("process_tool_results", process_tool_results_node)
    workflow.add_node("format_response", format_response_node)

    # Set entry point to planning
    workflow.set_entry_point("planning")

    # Add conditional edge from planning
    workflow.add_conditional_edges(
        "planning",
        should_continue_after_planning,
        {
            "task_node": "task_node",
            "format_response": "format_response"
        }
    )

    # Add conditional edge from task_node to check for tool calls
    workflow.add_conditional_edges(
        "task_node",
        tools_condition,
        {
            "tools": "tools",
            "__end__": "process_tool_results"
        }
    )

    # Add conditional edge from process_tool_results
    workflow.add_conditional_edges(
        "process_tool_results",
        should_continue_after_task,
        {
            "task_node": "task_node",
            "format_response": "format_response"
        }
    )

    # Add edge from tools back to process_tool_results
    workflow.add_edge("tools", "process_tool_results")

    # Add edge from format_response to END
    workflow.add_edge("format_response", END)

    # Add memory for state persistence
    memory = MemorySaver()

    # Compile the graph
    app = workflow.compile(checkpointer=memory)

    return app


def run_agent(input_text: str, sender_email: str, thread_id: str = "default") -> dict:
    """
    Run the simplified agent with the given input.

    Args:
        input_text: Text containing instructions to process
        thread_id: Thread ID for conversation tracking

    Returns:
        Agent response with processed instructions and metadata
    """
    # Create the agent graph
    app = create_agent_graph()

    # Create initial state with loop parameters
    initial_state = create_initial_state(input_text)
    initial_state.update({
        "sender_email": sender_email,
        "current_step": 0,
        "max_steps": 3,  # Reduced to 3 steps to prevent loops
        "needs_action": False
    })

    # Configure the thread
    config = {"configurable": {"thread_id": thread_id}}

    try:
        # Run the agent
        result = app.invoke(initial_state, config=config)

        return {
            "success": True,
            "output": result.get("output", "No output generated"),
            "pending_emails": result.get("pending_emails", []),
            "metadata": {
                "model_used": result.get("model_used"),
                "processing_time": result.get("processing_time"),
                "timestamp": result.get("timestamp"),
                "thread_id": thread_id,
                "steps_taken": result.get("current_step", 0)
            },
            "error": result.get("error")
        }

    except Exception as e:
        return {
            "success": False,
            "output": None,
            "metadata": {
                "thread_id": thread_id
            },
            "error": f"Agent execution failed: {str(e)}"
        }


# Example usage
if __name__ == "__main__":
    # Test the agent with multiple instructions
    test_input = """
    Hi there! I need help with a few things:

    1. Please explain what artificial intelligence is
    2. Can you tell me the weather forecast for tomorrow?
    3. Help me write a brief email to my team about our meeting next week

    Thanks for your assistance!
    """

    result = run_agent(test_input)
    print("Agent Result:")
    print(f"Success: {result['success']}")
    print(f"Output: {result['output']}")
    print(f"Error: {result['error']}")
    print(f"Metadata: {result['metadata']}")
