"""Response formatting node for the LangGraph agent."""

import logging
from typing import Dict, Any
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain.schema import HumanMessage

from config.settings import settings
from agent.state import AgentState

logger = logging.getLogger(__name__)


def format_response_node(state: AgentState) -> Dict[str, Any]:
    """
    Simplified node that formats the final response based on all processing steps.

    Args:
        state: Current agent state

    Returns:
        Updated state with formatted output
    """
    try:
        # Check for errors first
        if state.get("error"):
            return {
                "output": f"<PERSON><PERSON>: {state['error']}"
            }

        # Get current context
        messages = state.get("messages", [])
        input_text = state.get("input", "")
        pending_emails = state.get("pending_emails", [])
        sender_email = state.get("sender_email", "")
        plan = state.get("plan")

        # If plan exists but needs clarification, return clarification questions
        if plan and plan.clarification_needed:
            output = f"Ich habe Ihren Auftrag analysiert: {plan.plan_summary}\n\n"
            output += "Bevor ich fortfahren kann, benötige ich Klärung zu folgenden Punkten:\n\n"
            for i, clarification in enumerate(plan.clarification_needed, 1):
                output += f"{i}. {clarification}\n"
            output += "\nBitte geben Sie mir diese Informationen, damit ich Ihnen optimal helfen kann."
            return {
                "output": output
            }

        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=settings.agent_temperature,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        # Create context about completed tasks if plan exists
        task_context = ""
        if plan and plan.tasks:
            completed_tasks = [task for task in plan.tasks if task.completed]
            if completed_tasks:
                task_context = f"\n\nAusgeführte Aufgaben:\n"
                for task in completed_tasks:
                    task_context += f"- {task.description} (Ergebnis: {task.deliverable})\n"

        final_message = HumanMessage(content=f"""
Du prüfst den gesamten Message-Thread und gibst eine zusammenfassende Antwort an den Benutzer.
Die Nachricht solle alle relevanten Informationen enthalten, die der Benutzer benötigt.
Die Nachricht soll Informationen über alle Aktionen enthalten, die ausgeführt wurden.
Antworte IMMER auf Deutsch.
Lasse abschließende Worte wie "kann ich dir noch helfen" weg und antworte möglichst präzise und direkt.

Verfasse die Nachricht als Antwort auf diese E-Mail:
{input_text}

Der Empfänger ist: {sender_email}

{task_context}
        """)

        final_response = llm.invoke(messages + [final_message])

        # Create the main response
        output = final_response.content

        # Add plan summary if available
        if plan and not plan.clarification_needed:
            output += f"\n\n📋 Ausgeführter Plan: {plan.plan_summary}"

        # Add pending emails information if any exist
        if pending_emails:
            output += "\n\n" + "="*50 + "\n"
            output += "📧 EMAILS ZUM VERSENDEN:\n"
            output += "="*50 + "\n\n"

            for i, email in enumerate(pending_emails, 1):
                output += f"Email #{i}:\n"
                output += f"  An: {email.to_email}\n"
                output += f"  Betreff: {email.subject}\n"
                output += f"  Von: {email.from_name or 'Standard-Absender'}\n"
                output += f"  Inhalt:\n"
                # Indent the email body
                body_lines = email.body.split('\n')
                for line in body_lines:
                    output += f"    {line}\n"
                output += "\n" + "-"*30 + "\n\n"

        return {
            "output": output
        }

    except Exception as e:
        return {
            "output": f"Fehler beim Formatieren der Antwort: {str(e)}"
        }
