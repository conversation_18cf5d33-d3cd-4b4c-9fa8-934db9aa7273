"""Task execution node for the LangGraph agent."""

import time
import logging
from typing import Dict, Any
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain.schema import HumanMessage

from config.settings import settings
from agent.state import AgentState
from agent.tools import AGENT_TOOLS

logger = logging.getLogger(__name__)


def task_node(state: AgentState) -> Dict[str, Any]:
    """
    Task execution node that works through the tasks in the plan.

    Args:
        state: Current agent state

    Returns:
        Updated state with task execution results
    """
    start_time = time.time()

    try:
        # Get current plan and messages
        plan = state.get("plan")
        messages = state.get("messages", [])
        current_step = state.get("current_step", 0)

        if not plan or not plan.tasks:
            return {
                "error": "Kein Plan verfügbar für die Ausführung",
                "processing_time": time.time() - start_time
            }

        # Get current task
        current_task_index = plan.current_task_index
        if current_task_index >= len(plan.tasks):
            # All tasks completed
            return {
                "needs_action": False,
                "current_step": current_step + 1,
                "processing_time": time.time() - start_time
            }

        current_task = plan.tasks[current_task_index]

        # Initialize the OpenAI model with tools
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.3,
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )

        # Bind tools to the model
        llm_with_tools = llm.bind_tools(AGENT_TOOLS)

        # Create task execution prompt
        task_message = HumanMessage(content=f"""
Führe die folgende Aufgabe aus:

Aufgabe: {current_task.description}
Erwartetes Ergebnis: {current_task.deliverable}

Verwende die verfügbaren Tools, um diese Aufgabe zu erfüllen.
Antworte auf Deutsch.
        """)

        # Execute task with tools
        response = llm_with_tools.invoke(messages + [task_message])

        processing_time = time.time() - start_time

        # Mark current task as completed and move to next
        plan.tasks[current_task_index].completed = True
        plan.current_task_index += 1

        # Store the action result
        action_results = state.get("action_results", [])
        action_results.append({
            "task_id": current_task.id,
            "task_description": current_task.description,
            "response": response.content if response.content else "Tool-Aufruf ausgeführt",
            "processing_time": processing_time
        })

        logger.info(f"Completed task {current_task.id}: {current_task.description}")

        # Check if there are more tasks to execute
        has_more_tasks = plan.current_task_index < len(plan.tasks)

        return {
            "plan": plan,
            "action_results": action_results,
            "current_step": current_step + 1,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "messages": [response],  # Add response for tool processing
            "needs_action": has_more_tasks  # Continue if more tasks
        }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in task node: {e}")
        return {
            "error": f"Fehler bei der Aufgabenausführung: {str(e)}",
            "processing_time": processing_time,
            "current_step": state.get("current_step", 0) + 1
        }
