"""Planning node for the LangGraph agent."""

import time
import logging
from typing import Dict, Any
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain.schema import HumanMessage, SystemMessage

from config.settings import settings
from agent.state import AgentState, ExecutionPlan
from agent.prompts.analyze_messages import get_analyze_messages_system_prompt

logger = logging.getLogger(__name__)


def planning_node(state: AgentState) -> Dict[str, Any]:
    """
    Initial planning node that creates an execution plan from the email input.

    Args:
        state: Current agent state

    Returns:
        Updated state with execution plan
    """
    start_time = time.time()

    try:
        # Get input text
        input_text = state.get("input", "")

        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.3,
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )

        # Create planning prompt using the dynamic analyze_messages system prompt
        system_message = SystemMessage(content=get_analyze_messages_system_prompt())
        human_message = HumanMessage(content=input_text)

        # Get plan from OpenAI with structured output
        response = llm.with_structured_output(ExecutionPlan).invoke([system_message, human_message])

        processing_time = time.time() - start_time

        logger.info(f"Created plan with {len(response.tasks)} tasks: {response.plan_summary}")

        return {
            "plan": response,
            "processing_time": processing_time,
            "model_used": settings.agent_model,
            "messages": [system_message, human_message]
        }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in planning node: {e}")
        return {
            "error": f"Fehler bei der Planung: {str(e)}",
            "processing_time": processing_time
        }
