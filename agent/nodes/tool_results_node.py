"""Tool results processing node for the LangGraph agent."""

import json
import logging
from typing import Dict, Any, List
from langchain.schema import BaseMessage

from agent.state import AgentState, EmailMessage

logger = logging.getLogger(__name__)


def extract_emails_from_messages(messages: List[BaseMessage]) -> List[EmailMessage]:
    """
    Extract EmailMessage objects from tool call responses in messages.

    Args:
        messages: List of messages that may contain tool call responses

    Returns:
        List of EmailMessage objects found in the messages
    """
    emails = []

    for message in messages:
        # Check if this is an AI message with tool calls
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                # Check if this is a create_email tool call
                if tool_call.get('name') == 'create_email':
                    try:
                        # The tool returns a string starting with "EMAIL_CREATED:"
                        # We need to look at the actual tool response, not just the call
                        pass
                    except Exception as e:
                        logger.error(f"Error extracting email from tool call: {e}")

        # Check message content for EMAIL_CREATED responses
        if hasattr(message, 'content') and message.content:
            content = str(message.content)
            if "EMAIL_CREATED:" in content:
                try:
                    # Extract JSON part after EMAIL_CREATED:
                    json_start = content.find("EMAIL_CREATED:") + len("EMAIL_CREATED:")
                    json_str = content[json_start:].strip()

                    # Find the end of the JSON (look for the closing brace)
                    brace_count = 0
                    json_end = 0
                    for i, char in enumerate(json_str):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break

                    if json_end > 0:
                        json_str = json_str[:json_end]
                        email_data = json.loads(json_str)
                        email = EmailMessage(**email_data)
                        emails.append(email)

                except Exception as e:
                    logger.error(f"Error parsing email from message content: {e}")

    return emails


def process_tool_results_node(state: AgentState) -> Dict[str, Any]:
    """
    Process tool results and extract any emails that were created.

    Args:
        state: Current agent state

    Returns:
        Updated state with extracted emails and incremented step counter
    """
    try:
        # Get current messages and pending emails
        messages = state.get("messages", [])
        current_pending_emails = state.get("pending_emails", [])
        current_step = state.get("current_step", 0)

        # Extract emails from tool responses
        new_emails = extract_emails_from_messages(messages)

        # Add new emails to pending list
        all_pending_emails = current_pending_emails + new_emails

        logger.info(f"Extracted {len(new_emails)} emails from tool responses")

        return {
            "pending_emails": all_pending_emails,
        }

    except Exception as e:
        logger.error(f"Error processing tool results: {e}")
        return {
            "error": f"Fehler beim Verarbeiten der Tool-Ergebnisse: {str(e)}"
        }
