"""Node registry for the <PERSON><PERSON><PERSON>h agent."""

from .planning_node import planning_node
from .task_node import task_node
from .format_response_node import format_response_node
from .tool_results_node import process_tool_results_node

# Registry of all available nodes
AGENT_NODES = {
    "planning_node": planning_node,
    "task_node": task_node,
    "format_response_node": format_response_node,
    "process_tool_results_node": process_tool_results_node
}

# Node descriptions for documentation
NODE_DESCRIPTIONS = {
    "planning_node": {
        "name": "planning_node",
        "description": "Initial planning node that creates an execution plan from the email input",
        "inputs": ["input"],
        "outputs": ["plan", "processing_time", "model_used", "messages"]
    },
    "task_node": {
        "name": "task_node", 
        "description": "Task execution node that works through the tasks in the plan",
        "inputs": ["plan", "messages", "current_step"],
        "outputs": ["plan", "action_results", "current_step", "model_used", "processing_time", "messages", "needs_action"]
    },
    "format_response_node": {
        "name": "format_response_node",
        "description": "Formats the final response based on all processing steps",
        "inputs": ["messages", "input", "pending_emails", "sender_email", "plan"],
        "outputs": ["output"]
    },
    "process_tool_results_node": {
        "name": "process_tool_results_node",
        "description": "Process tool results and extract any emails that were created",
        "inputs": ["messages", "pending_emails", "current_step"],
        "outputs": ["pending_emails", "current_step"]
    }
}
