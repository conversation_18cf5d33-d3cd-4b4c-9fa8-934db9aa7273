"""Simplified node implementations for the LangGraph agent."""

import time
import re
import json
import logging
from typing import Dict, Any, List, Optional
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage, BaseMessage
from pydantic import BaseModel, Field

from config.settings import settings
from agent.state import AgentState, EmailMessage, ExecutionPlan, PlanTask
from agent.tools import AGENT_TOOLS
from agent.prompts.analyze_messages import get_analyze_messages_system_prompt

logger = logging.getLogger(__name__)

class ModelDecission(BaseModel):
    """Decide if we need to do more research or if we can answer the question."""

    decission: str = Field(
        description="Decission score: 'yes' or 'no'"
    )

    nextTask: str = Field(
        description="The next task to perform."
    )


def extract_emails_from_messages(messages: List[BaseMessage]) -> List[EmailMessage]:
    """
    Extract EmailMessage objects from tool call responses in messages.

    Args:
        messages: List of messages that may contain tool call responses

    Returns:
        List of EmailMessage objects found in the messages
    """
    emails = []

    for message in messages:
        # Check if this is an AI message with tool calls
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                # Check if this is a create_email tool call
                if tool_call.get('name') == 'create_email':
                    try:
                        # The tool returns a string starting with "EMAIL_CREATED:"
                        # We need to look at the actual tool response, not just the call
                        pass
                    except Exception as e:
                        logger.error(f"Error extracting email from tool call: {e}")

        # Check message content for EMAIL_CREATED responses
        if hasattr(message, 'content') and message.content:
            content = str(message.content)
            if "EMAIL_CREATED:" in content:
                try:
                    # Extract JSON part after EMAIL_CREATED:
                    json_start = content.find("EMAIL_CREATED:") + len("EMAIL_CREATED:")
                    json_str = content[json_start:].strip()

                    # Find the end of the JSON (look for the closing brace)
                    brace_count = 0
                    json_end = 0
                    for i, char in enumerate(json_str):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break

                    if json_end > 0:
                        json_str = json_str[:json_end]
                        email_data = json.loads(json_str)
                        email = EmailMessage(**email_data)
                        emails.append(email)

                except Exception as e:
                    logger.error(f"Error parsing email from message content: {e}")

    return emails



# ============================================================================
# PLANNING-BASED NODES
# ============================================================================

def planning_node(state: AgentState) -> Dict[str, Any]:
    """
    Initial planning node that creates an execution plan from the email input.

    Args:
        state: Current agent state

    Returns:
        Updated state with execution plan
    """
    start_time = time.time()

    try:
        # Get input text
        input_text = state.get("input", "")

        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.3,
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )

        # Create planning prompt using the dynamic analyze_messages system prompt
        system_message = SystemMessage(content=get_analyze_messages_system_prompt())
        human_message = HumanMessage(content=input_text)

        # Get plan from OpenAI with structured output
        response = llm.with_structured_output(ExecutionPlan).invoke([system_message, human_message])

        processing_time = time.time() - start_time

        logger.info(f"Created plan with {len(response.tasks)} tasks: {response.plan_summary}")

        return {
            "plan": response,
            "processing_time": processing_time,
            "model_used": settings.agent_model,
            "messages": [system_message, human_message]
        }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in planning node: {e}")
        return {
            "error": f"Fehler bei der Planung: {str(e)}",
            "processing_time": processing_time
        }


def task_node(state: AgentState) -> Dict[str, Any]:
    """
    Task execution node that works through the tasks in the plan.

    Args:
        state: Current agent state

    Returns:
        Updated state with task execution results
    """
    start_time = time.time()

    try:
        # Get current plan and messages
        plan = state.get("plan")
        messages = state.get("messages", [])
        current_step = state.get("current_step", 0)

        if not plan or not plan.tasks:
            return {
                "error": "Kein Plan verfügbar für die Ausführung",
                "processing_time": time.time() - start_time
            }

        # Get current task
        current_task_index = plan.current_task_index
        if current_task_index >= len(plan.tasks):
            # All tasks completed
            return {
                "needs_action": False,
                "current_step": current_step + 1,
                "processing_time": time.time() - start_time
            }

        current_task = plan.tasks[current_task_index]

        # Initialize the OpenAI model with tools
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.3,
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )

        # Bind tools to the model
        llm_with_tools = llm.bind_tools(AGENT_TOOLS)

        # Create task execution prompt
        task_message = HumanMessage(content=f"""
Führe die folgende Aufgabe aus:

Aufgabe: {current_task.description}
Erwartetes Ergebnis: {current_task.deliverable}

Verwende die verfügbaren Tools, um diese Aufgabe zu erfüllen.
Antworte auf Deutsch.
        """)

        # Execute task with tools
        response = llm_with_tools.invoke(messages + [task_message])

        processing_time = time.time() - start_time

        # Mark current task as completed and move to next
        plan.tasks[current_task_index].completed = True
        plan.current_task_index += 1

        # Store the action result
        action_results = state.get("action_results", [])
        action_results.append({
            "task_id": current_task.id,
            "task_description": current_task.description,
            "response": response.content if response.content else "Tool-Aufruf ausgeführt",
            "processing_time": processing_time
        })

        logger.info(f"Completed task {current_task.id}: {current_task.description}")

        # Check if there are more tasks to execute
        has_more_tasks = plan.current_task_index < len(plan.tasks)

        return {
            "plan": plan,
            "action_results": action_results,
            "current_step": current_step + 1,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "messages": [response],  # Add response for tool processing
            "needs_action": has_more_tasks  # Continue if more tasks
        }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in task node: {e}")
        return {
            "error": f"Fehler bei der Aufgabenausführung: {str(e)}",
            "processing_time": processing_time,
            "current_step": state.get("current_step", 0) + 1
        }


def format_response_node(state: AgentState) -> Dict[str, Any]:
    """
    Simplified node that formats the final response based on all processing steps.

    Args:
        state: Current agent state

    Returns:
        Updated state with formatted output
    """
    try:
        # Check for errors first
        if state.get("error"):
            return {
                "output": f"Fehler: {state['error']}"
            }

        # Get current context
        messages = state.get("messages", [])
        input_text = state.get("input", "")
        pending_emails = state.get("pending_emails", [])
        sender_email = state.get("sender_email", "")
        plan = state.get("plan")

        # If plan exists but needs clarification, return clarification questions
        if plan and plan.clarification_needed:
            output = f"Ich habe Ihren Auftrag analysiert: {plan.plan_summary}\n\n"
            output += "Bevor ich fortfahren kann, benötige ich Klärung zu folgenden Punkten:\n\n"
            for i, clarification in enumerate(plan.clarification_needed, 1):
                output += f"{i}. {clarification}\n"
            output += "\nBitte geben Sie mir diese Informationen, damit ich Ihnen optimal helfen kann."
            return {
                "output": output
            }

        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=settings.agent_temperature,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        # Create context about completed tasks if plan exists
        task_context = ""
        if plan and plan.tasks:
            completed_tasks = [task for task in plan.tasks if task.completed]
            if completed_tasks:
                task_context = f"\n\nAusgeführte Aufgaben:\n"
                for task in completed_tasks:
                    task_context += f"- {task.description} (Ergebnis: {task.deliverable})\n"

        final_message = HumanMessage(content=f"""
Du prüfst den gesamten Message-Thread und gibst eine zusammenfassende Antwort an den Benutzer.
Die Nachricht solle alle relevanten Informationen enthalten, die der Benutzer benötigt.
Die Nachricht soll Informationen über alle Aktionen enthalten, die ausgeführt wurden.
Antworte IMMER auf Deutsch.
Lasse abschließende Worte wie "kann ich dir noch helfen" weg und antworte möglichst präzise und direkt.

Verfasse die Nachricht als Antwort auf diese E-Mail:
{input_text}

Der Empfänger ist: {sender_email}

{task_context}
        """)

        final_response = llm.invoke(messages + [final_message])

        # Create the main response
        output = final_response.content

        # Add plan summary if available
        if plan and not plan.clarification_needed:
            output += f"\n\n📋 Ausgeführter Plan: {plan.plan_summary}"

        # Add pending emails information if any exist
        if pending_emails:
            output += "\n\n" + "="*50 + "\n"
            output += "📧 EMAILS ZUM VERSENDEN:\n"
            output += "="*50 + "\n\n"

            for i, email in enumerate(pending_emails, 1):
                output += f"Email #{i}:\n"
                output += f"  An: {email.to_email}\n"
                output += f"  Betreff: {email.subject}\n"
                output += f"  Von: {email.from_name or 'Standard-Absender'}\n"
                output += f"  Inhalt:\n"
                # Indent the email body
                body_lines = email.body.split('\n')
                for line in body_lines:
                    output += f"    {line}\n"
                output += "\n" + "-"*30 + "\n\n"

        return {
            "output": output
        }

    except Exception as e:
        return {
            "output": f"Fehler beim Formatieren der Antwort: {str(e)}"
        }


def process_tool_results_node(state: AgentState) -> Dict[str, Any]:
    """
    Process tool results and extract any emails that were created.

    Args:
        state: Current agent state

    Returns:
        Updated state with extracted emails and incremented step counter
    """
    try:
        # Get current messages and pending emails
        messages = state.get("messages", [])
        current_pending_emails = state.get("pending_emails", [])
        current_step = state.get("current_step", 0)

        # Extract emails from tool responses
        new_emails = extract_emails_from_messages(messages)

        # Add new emails to pending list
        all_pending_emails = current_pending_emails + new_emails

        logger.info(f"Extracted {len(new_emails)} emails from tool responses")

        return {
            "pending_emails": all_pending_emails,
            "current_step": current_step + 1
        }

    except Exception as e:
        logger.error(f"Error processing tool results: {e}")
        return {
            "current_step": state.get("current_step", 0) + 1,
            "error": f"Fehler beim Verarbeiten der Tool-Ergebnisse: {str(e)}"
        }

