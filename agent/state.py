"""State management for the LangGraph agent."""

from typing import TypedDict, Optional, List, Dict, Any, Annotated
from datetime import datetime
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field

class EmailMessage(BaseModel):
    to_email: str = Field()
    subject: str = Field()
    body: str = Field()
    from_name: Optional[str] = Field(default=None)

class PlanTask(BaseModel):
    """Individual task in the execution plan."""
    id: str = Field(description="Task identifier (e.g., T1, T2)")
    description: str = Field(description="Imperative-verb task description")
    deliverable: str = Field(description="Expected output format")
    completed: bool = Field(default=False, description="Whether task is completed")

class ExecutionPlan(BaseModel):
    """Complete execution plan for the agent."""
    plan_summary: str = Field(description="1-sentence objective")
    tasks: List[PlanTask] = Field(description="List of tasks to execute")
    clarification_needed: List[str] = Field(default_factory=list, description="Unclear requirements")
    current_task_index: int = Field(default=0, description="Index of current task being executed")

class AgentState(TypedDict):
    """State for the planning-based instruction processing agent."""

    # Input and output
    sender_email: str
    input: str
    output: Optional[str]

    # Messages for LangGraph tool integration
    messages: Annotated[List[BaseMessage], add_messages]

    # Planning system
    plan: Optional[ExecutionPlan]

    # Loop control
    current_step: Optional[int]
    max_steps: Optional[int]
    needs_action: Optional[bool]

    # Analysis and action results
    analysis: Optional[str]
    action_results: Optional[List[Dict[str, Any]]]

    # Email management
    pending_emails: Optional[List[EmailMessage]]

    # Processing metadata
    timestamp: Optional[str]
    model_used: Optional[str]
    processing_time: Optional[float]

    # Error handling
    error: Optional[str]

    # Additional context
    metadata: Optional[Dict[str, Any]]


def create_initial_state(input_text: str) -> AgentState:
    """Create initial state for the planning-based agent."""
    return AgentState(
        input=input_text,
        output=None,
        messages=[],
        plan=None,
        current_step=0,
        max_steps=5,
        needs_action=False,
        analysis=None,
        action_results=[],
        pending_emails=[],
        timestamp=datetime.now().isoformat(),
        model_used=None,
        processing_time=None,
        error=None,
        metadata={}
    )
