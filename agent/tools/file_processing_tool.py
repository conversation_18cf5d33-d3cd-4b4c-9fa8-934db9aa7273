"""File processing tool for the LangGraph agent."""

import logging
import os
import base64
import sqlite3
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from openai import OpenAI

from config.settings import settings

logger = logging.getLogger(__name__)
client = OpenAI()


@tool
def process_file_with_llm(attachment_id: str, prompt: str) -> str:
    """
    Process a file attachment (image or PDF) with a custom prompt.

    This tool can process the content of a file attachment.
    It supports images (PNG, JPEG, GIF, WebP) and PDF files.

    Args:
        attachment_id: The unique ID of the attachment to process
        prompt: The prompt/question to send to the LLM along with the file. It must be self-contained with all the need context.

    Returns:
        The response to the prompt about the file content in German
    """
    try:
        # Initialize database connection
        from email_agent.database import EmailDatabase
        database = EmailDatabase()

        # Get attachment information
        with sqlite3.connect(database.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT filename, content_type, file_path, file_size
                FROM email_attachments
                WHERE unique_id = ? AND is_relevant = TRUE
            """, (attachment_id,))

            result = cursor.fetchone()
            if not result:
                return f"Anhang mit ID '{attachment_id}' nicht gefunden oder nicht relevant."

            filename, content_type, file_path, file_size = result

        # Check if file exists
        if not os.path.exists(file_path):
            return f"Datei nicht gefunden: {file_path}"

        # Check file type and size
        max_size_mb = 20  # OpenAI limit for vision
        file_size_mb = file_size / (1024 * 1024)

        if file_size_mb > max_size_mb:
            return f"Datei zu groß ({file_size_mb:.2f} MB). Maximum: {max_size_mb} MB."

        # Initialize OpenAI client
        llm = ChatOpenAI(
            model="gpt-4o",  # Use GPT-4 Vision for file processing
            temperature=0.7,
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )

        # Process based on file type
        if content_type.startswith('image/'):
            return _process_image_with_openai(file_path, filename, prompt, llm)
        elif content_type == 'application/pdf':
            return _process_pdf_with_openai(file_path, filename, prompt, llm)
        else:
            return f"Dateityp '{content_type}' wird nicht unterstützt. Unterstützte Typen: Bilder (PNG, JPEG, GIF, WebP) und PDF-Dateien."

    except Exception as e:
        error_msg = f"Fehler beim Verarbeiten der Datei: {str(e)}"
        logger.error(error_msg)
        return error_msg


def _process_image_with_openai(file_path: str, filename: str, prompt: str, llm: ChatOpenAI) -> str:
    """Process an image file with OpenAI Vision API."""
    try:
        # Read and encode image
        with open(file_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')

        # Determine image format
        file_extension = os.path.splitext(filename)[1].lower()
        if file_extension == '.jpg' or file_extension == '.jpeg':
            image_format = 'jpeg'
        elif file_extension == '.png':
            image_format = 'png'
        elif file_extension == '.gif':
            image_format = 'gif'
        elif file_extension == '.webp':
            image_format = 'webp'
        else:
            image_format = 'jpeg'  # Default fallback

        # Create message with image
        system_message = SystemMessage(content="""Du bist ein hilfreicher Assistent, der Bilder analysiert und auf Deutsch antwortet.
Analysiere das bereitgestellte Bild sorgfältig und beantworte die gestellte Frage präzise und detailliert.""")

        # Create human message with image
        human_message = HumanMessage(
            content=[
                {
                    "type": "text",
                    "text": f"Dateiname: {filename}\n\nFrage/Aufgabe: {prompt}"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/{image_format};base64,{image_data}"
                    }
                }
            ]
        )

        # Get response from OpenAI
        response = llm.invoke([system_message, human_message])

        return f"Analyse von '{filename}':\n\n{response.content}"

    except Exception as e:
        logger.error(f"Error processing image {filename}: {e}")
        return f"Fehler beim Verarbeiten des Bildes '{filename}': {str(e)}"


def _process_pdf_with_openai(file_path: str, filename: str, prompt: str, llm: ChatOpenAI) -> str:
    """Process a PDF file with OpenAI API."""

    file = client.files.create(
        file=open(file_path, "rb"),
        purpose="user_data"
    )

    completion = client.chat.completions.create(
        model="gpt-4.1",
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "file",
                        "file": {
                            "file_id": file.id,
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt
                    },
                ]
            }
        ]
    )

    return completion.choices[0].message.content
