"""Tool registry for the <PERSON><PERSON><PERSON><PERSON> agent."""

from .email_tool import create_email
from .attachment_tools import get_file_attachment, list_email_attachments
from .file_processing_tool import process_file_with_llm
from .accounting_tool import accounting
from shared.tools_registry import tools_registry

# List of all available agent tools (for backward compatibility)
AGENT_TOOLS = [
    create_email,
    get_file_attachment,
    list_email_attachments,
    process_file_with_llm,
    accounting
]

# Tool descriptions for dynamic prompt generation (for backward compatibility)
TOOL_DESCRIPTIONS = {
    "create_email": {
        "name": "create_email",
        "description": "Create an email message that will be queued for sending. This tool creates an EmailMessage object instead of directly sending the email. The email will be added to the pending emails list and shown to the user for approval.",
        "category": "communication"
    },
    "get_file_attachment": {
        "name": "get_file_attachment",
        "description": "Retrieve information about a file attachment from an email. This tool allows the agent to access information about file attachments that were received via email. IT WILL NOT RETURN THE FILE CONTENT ITSELF. It returns information about the attachment including its path, so the agent can read or process the file content.",
        "category": "file_management"
    },
    "list_email_attachments": {
        "name": "list_email_attachments",
        "description": "List all available email attachments that the agent can access. This tool shows all file attachments from processed emails that are classified as relevant (not logos or signatures).",
        "category": "file_management"
    },
    "process_file_with_llm": {
        "name": "process_file_with_llm",
        "description": "Process a file attachment (image or PDF) with a custom prompt. This tool can process the content of a file attachment. It supports images (PNG, JPEG, GIF, WebP) and PDF files.",
        "category": "file_processing"
    },
    "accounting": {
        "name": "accounting",
        "description": "Führt Buchhaltungsoperationen über die konfigurierte Buchhaltungssoftware aus. Dieses Tool leitet Anfragen an die konfigurierte Buchhaltungssoftware weiter (z.B. Bookamat). Es kann Buchungen erstellen, Konten abrufen und andere buchhaltungsrelevante Aufgaben ausführen.",
        "category": "accounting"
    }
}

def get_tools_description() -> str:
    """Generate a formatted description of all available tools for system prompts."""
    # Use the shared registry for dynamic tool descriptions
    return tools_registry.get_tools_description_text()

def get_agent_tools(categories=None):
    """Get agent tools from the shared registry."""
    return tools_registry.get_tools(categories)

def get_shared_tools(categories=None):
    """Get tools from the shared registry that can be used by sub-agents."""
    # Return all tools except accounting (which is handled by subgraphs)
    excluded_categories = ["accounting"] if categories is None else []
    all_categories = ["communication", "file_management", "file_processing"]

    if categories:
        filtered_categories = [cat for cat in categories if cat not in excluded_categories]
    else:
        filtered_categories = [cat for cat in all_categories if cat not in excluded_categories]

    return tools_registry.get_tools(filtered_categories)
