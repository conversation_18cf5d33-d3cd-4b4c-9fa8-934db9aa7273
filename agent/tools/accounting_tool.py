"""Accounting tool for the LangGraph agent."""

import logging
from typing import Optional
from langchain_core.tools import tool

from config.settings import settings

logger = logging.getLogger(__name__)

# Import accounting subgraph if configured
try:
    if settings.accounting_software == "bookamat":
        from plugins.bookamat.graph import run_bookamat_subgraph
        ACCOUNTING_AVAILABLE = True
    else:
        ACCOUNTING_AVAILABLE = False
        run_bookamat_subgraph = None
except ImportError:
    ACCOUNTING_AVAILABLE = False
    run_bookamat_subgraph = None


@tool
def accounting(prompt: str, files: Optional[str] = None) -> str:
    """
    Führt Buchhaltungsoperationen über die konfigurierte Buchhaltungssoftware aus.

    Dieses Tool leitet Anfragen an die konfigurierte Buchhaltungssoftware weiter (z.B. Bookamat).
    Es kann Buchungen erstellen, Konten abrufen und andere buchhaltungsrelevante Aufgaben ausführen.

    Args:
        prompt: Detaillierte Beschreibung der gewünschten Buchhaltungsoperation
        files: Optional - Referenz zu relevanten Dateien (z<PERSON><PERSON><PERSON>, Belege)

    Returns:
        Ergebnis der Buchhaltungsoperation in deutscher Sprache
    """
    try:
        if not ACCOUNTING_AVAILABLE:
            return f"❌ Buchhaltungssoftware '{settings.accounting_software}' ist nicht verfügbar oder nicht konfiguriert."

        # Prepare the full prompt with file information if provided
        full_prompt = prompt
        if files:
            full_prompt += f"\n\nRelevante Dateien: {files}"

        # Route to the appropriate accounting subgraph
        if settings.accounting_software == "bookamat":
            result = run_bookamat_subgraph(full_prompt)

            if result['success']:
                return result['output']
            else:
                error_msg = result.get('error', 'Unbekannter Fehler')
                return f"❌ Fehler bei der Buchhaltungsoperation: {error_msg}"
        else:
            return f"❌ Buchhaltungssoftware '{settings.accounting_software}' wird noch nicht unterstützt."

    except Exception as e:
        error_msg = f"Fehler beim Ausführen der Buchhaltungsoperation: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
