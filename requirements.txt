# Core LangGraph and LangChain dependencies
langgraph>=0.2.0
langchain>=0.2.0
langchain-openai>=0.1.0
langsmith>=0.1.0

# Web framework
flask>=3.0.0
flask-cors>=4.0.0

# Environment and configuration
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Utilities
requests>=2.31.0
typing-extensions>=4.8.0

# Email functionality
email-validator>=2.1.0

# File processing
PyPDF2>=3.0.0
Pillow>=10.0.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
