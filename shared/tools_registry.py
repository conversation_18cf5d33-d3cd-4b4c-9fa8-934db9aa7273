"""Shared tools registry system for the LangGraph agent and sub-agents."""

import logging
from typing import Dict, List, Any, Optional
from langchain_core.tools import BaseTool

from config.settings import settings

logger = logging.getLogger(__name__)


class ToolsRegistry:
    """
    Centralized registry for tools that can be shared between the main agent and sub-agents.
    """
    
    def __init__(self):
        self._tools: Dict[str, BaseTool] = {}
        self._tool_descriptions: Dict[str, Dict[str, Any]] = {}
        self._categories: Dict[str, List[str]] = {}
        self._subgraphs: Dict[str, Dict[str, Any]] = {}
    
    def register_tool(self, tool: BaseTool, description: str, category: str = "general") -> None:
        """
        Register a tool in the registry.
        
        Args:
            tool: The tool instance to register
            description: Description of what the tool does
            category: Category for organizing tools
        """
        tool_name = tool.name
        self._tools[tool_name] = tool
        self._tool_descriptions[tool_name] = {
            "name": tool_name,
            "description": description,
            "category": category
        }
        
        if category not in self._categories:
            self._categories[category] = []
        if tool_name not in self._categories[category]:
            self._categories[category].append(tool_name)
        
        logger.debug(f"Registered tool: {tool_name} in category: {category}")
    
    def register_subgraph(self, name: str, description: str, available: bool = True) -> None:
        """
        Register a subgraph in the registry.
        
        Args:
            name: Name of the subgraph
            description: Description of what the subgraph does
            available: Whether the subgraph is currently available
        """
        self._subgraphs[name] = {
            "name": name,
            "description": description,
            "available": available
        }
        
        logger.debug(f"Registered subgraph: {name}")
    
    def get_tools(self, categories: Optional[List[str]] = None) -> List[BaseTool]:
        """
        Get tools, optionally filtered by categories.
        
        Args:
            categories: List of categories to filter by. If None, returns all tools.
            
        Returns:
            List of tools
        """
        if categories is None:
            return list(self._tools.values())
        
        filtered_tools = []
        for category in categories:
            if category in self._categories:
                for tool_name in self._categories[category]:
                    if tool_name in self._tools:
                        filtered_tools.append(self._tools[tool_name])
        
        return filtered_tools
    
    def get_tool_descriptions(self, categories: Optional[List[str]] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get tool descriptions, optionally filtered by categories.
        
        Args:
            categories: List of categories to filter by. If None, returns all descriptions.
            
        Returns:
            Dictionary of tool descriptions
        """
        if categories is None:
            return self._tool_descriptions.copy()
        
        filtered_descriptions = {}
        for category in categories:
            if category in self._categories:
                for tool_name in self._categories[category]:
                    if tool_name in self._tool_descriptions:
                        filtered_descriptions[tool_name] = self._tool_descriptions[tool_name]
        
        return filtered_descriptions
    
    def get_subgraphs(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all registered subgraphs.
        
        Returns:
            Dictionary of subgraph information
        """
        return self._subgraphs.copy()
    
    def get_tools_description_text(self, categories: Optional[List[str]] = None) -> str:
        """
        Generate a formatted description of tools for system prompts.
        
        Args:
            categories: List of categories to include. If None, includes all categories.
            
        Returns:
            Formatted string describing available tools
        """
        descriptions = self.get_tool_descriptions(categories)
        
        if not descriptions:
            return "No tools available."
        
        description_parts = ["### Available Tools\n"]
        
        # Group by category
        categories_dict = {}
        for tool_name, tool_info in descriptions.items():
            category = tool_info["category"]
            if category not in categories_dict:
                categories_dict[category] = []
            categories_dict[category].append(tool_info)
        
        for category, tools in categories_dict.items():
            description_parts.append(f"#### {category.title()} Tools")
            for tool in tools:
                description_parts.append(f"**{tool['name']}**: {tool['description']}")
            description_parts.append("")
        
        return "\n".join(description_parts)
    
    def get_subgraphs_description_text(self) -> str:
        """
        Generate a formatted description of subgraphs for system prompts.
        
        Returns:
            Formatted string describing available subgraphs
        """
        subgraphs = self.get_subgraphs()
        
        if not subgraphs:
            return "No subgraphs available."
        
        description_parts = ["### Available Subgraphs\n"]
        
        for subgraph_name, subgraph_info in subgraphs.items():
            status = "✅ Available" if subgraph_info["available"] else "❌ Unavailable"
            description_parts.append(f"**{subgraph_info['name']}** ({status}): {subgraph_info['description']}")
        
        description_parts.append("")
        return "\n".join(description_parts)


# Global registry instance
tools_registry = ToolsRegistry()


def initialize_tools_registry():
    """
    Initialize the tools registry with all available tools and subgraphs.
    """
    logger.info("Initializing tools registry...")
    
    # Register main agent tools
    try:
        from agent.tools import (
            create_email, get_file_attachment, list_email_attachments, 
            process_file_with_llm, accounting
        )
        
        tools_registry.register_tool(
            create_email, 
            "Create an email message that will be queued for sending. This tool creates an EmailMessage object instead of directly sending the email. The email will be added to the pending emails list and shown to the user for approval.",
            "communication"
        )
        
        tools_registry.register_tool(
            get_file_attachment,
            "Retrieve information about a file attachment from an email. This tool allows the agent to access information about file attachments that were received via email. IT WILL NOT RETURN THE FILE CONTENT ITSELF. It returns information about the attachment including its path, so the agent can read or process the file content.",
            "file_management"
        )
        
        tools_registry.register_tool(
            list_email_attachments,
            "List all available email attachments that the agent can access. This tool shows all file attachments from processed emails that are classified as relevant (not logos or signatures).",
            "file_management"
        )
        
        tools_registry.register_tool(
            process_file_with_llm,
            "Process a file attachment (image or PDF) with a custom prompt. This tool can process the content of a file attachment. It supports images (PNG, JPEG, GIF, WebP) and PDF files.",
            "file_processing"
        )
        
        tools_registry.register_tool(
            accounting,
            "Führt Buchhaltungsoperationen über die konfigurierte Buchhaltungssoftware aus. Dieses Tool leitet Anfragen an die konfigurierte Buchhaltungssoftware weiter (z.B. Bookamat). Es kann Buchungen erstellen, Konten abrufen und andere buchhaltungsrelevante Aufgaben ausführen.",
            "accounting"
        )
        
        logger.info("Registered main agent tools")
        
    except ImportError as e:
        logger.warning(f"Could not import main agent tools: {e}")
    
    # Register subgraphs
    try:
        if settings.accounting_software == "bookamat":
            tools_registry.register_subgraph(
                "bookamat",
                "Bookamat accounting software integration for creating bookings, managing accounts, and other accounting operations",
                True
            )
        else:
            tools_registry.register_subgraph(
                "bookamat",
                "Bookamat accounting software integration (not configured)",
                False
            )
            
        logger.info("Registered subgraphs")
        
    except Exception as e:
        logger.warning(f"Could not register subgraphs: {e}")
    
    logger.info("Tools registry initialization complete")


# Initialize the registry when the module is imported
initialize_tools_registry()
