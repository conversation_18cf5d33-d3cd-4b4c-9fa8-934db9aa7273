"""
Bookamat API Client für die Integration mit der Bookamat Buchhaltungssoftware.
"""

import requests
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal

logger = logging.getLogger(__name__)


class BookamatAPIError(Exception):
    """Exception für Bookamat API Fehler."""
    pass


class BookamatClient:
    """
    Client für die Bookamat API.
    
    Dieser Client stellt Methoden für die Interaktion mit der Bookamat API bereit,
    einschließlich Authentifizierung und CRUD-Operationen für Buchungen.
    """
    
    def __init__(self, username: str, api_key: str, country: str = "at", year: int = None):
        """
        Initialisiert den Bookamat Client.
        
        Args:
            username: Bookamat Benutzername
            api_key: Bookamat API Key
            country: Land (Standard: "at" für Österreich)
            year: Jahr für das Paket (Standard: aktuelles Jahr)
        """
        self.username = username
        self.api_key = api_key
        self.country = country.lower()
        self.year = year or datetime.now().year
        self.base_url = f"https://www.bookamat.com/api/v1/{self.country}/{self.year}"
        
        # Session für wiederverwendbare Verbindungen
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'ApiKey {username}:{api_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Führt einen HTTP-Request zur Bookamat API aus.
        
        Args:
            method: HTTP-Methode (GET, POST, PUT, PATCH, DELETE)
            endpoint: API-Endpoint (ohne Base-URL)
            data: Request-Daten für POST/PUT/PATCH
            
        Returns:
            API-Response als Dictionary
            
        Raises:
            BookamatAPIError: Bei API-Fehlern
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, json=data)
            
            # Erfolgreiche Responses
            if response.status_code in [200, 201, 202]:
                return response.json() if response.content else {}
            
            # No Content (erfolgreiches Löschen)
            elif response.status_code == 204:
                return {}
            
            # Fehler-Behandlung
            else:
                error_msg = f"Bookamat API Fehler {response.status_code}"
                try:
                    error_data = response.json()
                    if isinstance(error_data, dict):
                        # Detaillierte Fehlermeldungen extrahieren
                        error_details = []
                        for field, messages in error_data.items():
                            if isinstance(messages, list):
                                error_details.extend(messages)
                            else:
                                error_details.append(str(messages))
                        if error_details:
                            error_msg += f": {', '.join(error_details)}"
                except:
                    error_msg += f": {response.text}"
                
                logger.error(f"Bookamat API Request fehlgeschlagen: {error_msg}")
                raise BookamatAPIError(error_msg)
                
        except requests.RequestException as e:
            error_msg = f"Netzwerk-Fehler bei Bookamat API: {str(e)}"
            logger.error(error_msg)
            raise BookamatAPIError(error_msg)
    
    def add_booking(self, booking_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fügt eine neue Buchung hinzu.
        
        Args:
            booking_data: Buchungsdaten gemäß Bookamat API Spezifikation
            
        Returns:
            Erstellte Buchung als Dictionary
        """
        return self._make_request("POST", "/bookings/", booking_data)
    
    def get_bookings(self, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Ruft Buchungen ab.
        
        Args:
            filters: Optional Filter-Parameter
            
        Returns:
            Liste der Buchungen
        """
        endpoint = "/bookings/"
        if filters:
            # Query-Parameter hinzufügen
            params = "&".join([f"{k}={v}" for k, v in filters.items()])
            endpoint += f"?{params}"
        
        return self._make_request("GET", endpoint)
    
    def get_bank_accounts(self) -> Dict[str, Any]:
        """Ruft verfügbare Zahlungsmittelkonten ab."""
        return self._make_request("GET", "/preferences/bankaccounts/")
    
    def get_cost_accounts(self) -> Dict[str, Any]:
        """Ruft verfügbare Steuerkonten ab."""
        return self._make_request("GET", "/preferences/costaccounts/")
    
    def get_purchasetax_accounts(self) -> Dict[str, Any]:
        """Ruft verfügbare Umsatzsteuerkonten ab."""
        return self._make_request("GET", "/preferences/purchasetaxaccounts/")
    
    def get_cost_centres(self) -> Dict[str, Any]:
        """Ruft verfügbare Kostenstellen ab."""
        return self._make_request("GET", "/preferences/costcentres/")
    
    def test_connection(self) -> bool:
        """
        Testet die Verbindung zur Bookamat API.
        
        Returns:
            True wenn die Verbindung erfolgreich ist, False sonst
        """
        try:
            self._make_request("GET", "/bookings/?limit=1")
            return True
        except BookamatAPIError:
            return False
