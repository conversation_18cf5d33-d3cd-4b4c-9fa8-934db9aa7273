"""Bookamat response formatting node."""

import logging
import time
from langchain_core.messages import AIMessage

from ..state import BookamatState

logger = logging.getLogger(__name__)


def bookamat_format_response_node(state: BookamatState) -> BookamatState:
    """
    Formatiert die finale Antwort für den Benutzer basierend auf den Ausführungsergebnissen.

    Args:
        state: Aktueller BookamatState

    Returns:
        BookamatState mit formatierter Ausgabe
    """
    try:
        start_time = time.time()

        # Check for errors first
        if state.get("error"):
            final_output = f"❌ Fehler: {state['error']}"
        else:
            # Get action results
            action_results = state.get("action_results", [])
            plan = state.get("plan")

            if action_results:
                # Format results from completed tasks
                output_parts = ["✅ Bookamat-Operationen abgeschlossen:\n"]

                for result in action_results:
                    output_parts.append(f"📋 {result['task_description']}")
                    if result['response']:
                        output_parts.append(f"   Ergebnis: {result['response']}")
                    output_parts.append("")

                # Add plan summary if available
                if plan and plan.plan_summary:
                    output_parts.insert(1, f"🎯 Plan: {plan.plan_summary}\n")

                final_output = "\n".join(output_parts).strip()

            elif plan and plan.clarification_needed:
                # Need clarification
                final_output = "❓ Für die Ausführung werden weitere Informationen benötigt:\n\n"
                for i, clarification in enumerate(plan.clarification_needed, 1):
                    final_output += f"{i}. {clarification}\n"
                final_output += "\nBitte geben Sie die fehlenden Informationen an."

            else:
                # No specific results
                final_output = "ℹ️ Bookamat-Anfrage verarbeitet, aber keine spezifischen Ergebnisse generiert."

        processing_time = time.time() - start_time

        # Create final response message
        response_message = AIMessage(content=final_output)

        return {
            **state,
            "messages": state["messages"] + [response_message],
            "output": final_output,
            "processing_time": processing_time
        }

    except Exception as e:
        error_msg = f"Fehler bei der Antwort-Formatierung: {str(e)}"
        logger.error(error_msg)

        return {
            **state,
            "error": error_msg,
            "output": f"❌ Antwort-Fehler: {error_msg}"
        }
