"""Bookamat nodes registry."""

from .planning_node import bookamat_planning_node
from .task_node import bookamat_task_node
from .format_response_node import bookamat_format_response_node
from .tool_results_node import process_bookamat_tool_results_node

# Registry of all available Bookamat nodes
BOOKAMAT_NODES = {
    "bookamat_planning_node": bookamat_planning_node,
    "bookamat_task_node": bookamat_task_node,
    "bookamat_format_response_node": bookamat_format_response_node,
    "process_bookamat_tool_results_node": process_bookamat_tool_results_node
}

# Node descriptions for documentation
BOOKAMAT_NODE_DESCRIPTIONS = {
    "bookamat_planning_node": {
        "name": "bookamat_planning_node",
        "description": "Erstellt einen Ausführungsplan für die Bookamat-Anfrage",
        "inputs": ["input"],
        "outputs": ["plan", "model_used", "processing_time", "timestamp"]
    },
    "bookamat_task_node": {
        "name": "bookamat_task_node", 
        "description": "<PERSON>ührt die nächste Aufgabe im Bookamat-Plan aus",
        "inputs": ["plan", "messages", "current_step"],
        "outputs": ["plan", "action_results", "current_step", "model_used", "processing_time", "messages", "needs_action"]
    },
    "bookamat_format_response_node": {
        "name": "bookamat_format_response_node",
        "description": "Formatiert die finale Antwort für den Benutzer basierend auf den Ausführungsergebnissen",
        "inputs": ["action_results", "plan", "messages"],
        "outputs": ["messages", "output", "processing_time"]
    },
    "process_bookamat_tool_results_node": {
        "name": "process_bookamat_tool_results_node",
        "description": "Verarbeitet Tool-Ergebnisse im Bookamat Subgraph",
        "inputs": ["messages", "current_step"],
        "outputs": ["current_step"]
    }
}
