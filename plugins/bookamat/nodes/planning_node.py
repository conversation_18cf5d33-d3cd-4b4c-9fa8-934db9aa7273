"""Bookamat planning node."""

import logging
import time
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from ..state import BookamatState
from agent.state import ExecutionPlan, PlanTask
from ..tools import get_account_information, get_bookamat_tools_description
from config.settings import settings

logger = logging.getLogger(__name__)


class BookamatPlan(BaseModel):
    """Ausführungsplan für Bookamat-Operationen."""
    plan_summary: str = Field(description="1-Satz Zusammenfassung des Plans")
    tasks: list[PlanTask] = Field(description="Liste der auszuführenden Aufgaben")
    clarification_needed: list[str] = Field(default_factory=list, description="Benötigte Klarstellungen")
    current_task_index: int = Field(default=0, description="Index der aktuellen Aufgabe")


def bookamat_planning_node(state: BookamatState) -> BookamatState:
    """
    Erstellt einen Ausführungsplan für die Bookamat-Anfrage.

    Args:
        state: Aktueller BookamatState

    Returns:
        Aktualisierter BookamatState mit Ausführungsplan
    """
    try:
        start_time = time.time()

        # Get account information and tool descriptions for system message
        account_info = get_account_information()
        tools_description = get_bookamat_tools_description()

        # Create LLM for planning
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=settings.agent_temperature,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        # System message with account information and dynamic tool descriptions
        system_message = SystemMessage(content=f"""Du bist ein Experte für Bookamat Buchhaltungssoftware.
Erstelle einen detaillierten Ausführungsplan für die gegebene Anfrage.

{account_info}

{tools_description}

Erstelle einen Plan mit konkreten, ausführbaren Aufgaben. Jede Aufgabe sollte:
- Eine klare Aktion beschreiben (mit Imperativ-Verb beginnen)
- Das erwartete Ergebnis definieren
- Alle notwendigen Parameter identifizieren

Antworte auf Deutsch.""")

        # User message with the request
        user_message = HumanMessage(content=f"""
Erstelle einen Ausführungsplan für folgende Anfrage:

{state['input']}

Analysiere die Anfrage und erstelle einen strukturierten Plan mit konkreten Aufgaben.
""")

        # Get planning response
        response = llm.with_structured_output(BookamatPlan).invoke([system_message, user_message])

        processing_time = time.time() - start_time

        # Convert to ExecutionPlan format
        execution_plan = ExecutionPlan(
            plan_summary=response.plan_summary,
            tasks=response.tasks,
            clarification_needed=response.clarification_needed,
            current_task_index=0
        )

        logger.info(f"Created Bookamat plan with {len(execution_plan.tasks)} tasks")

        return {
            **state,
            "plan": execution_plan,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "timestamp": time.time()
        }

    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Fehler bei der Bookamat Planung: {str(e)}"
        logger.error(error_msg)

        return {
            **state,
            "error": error_msg,
            "processing_time": processing_time
        }
