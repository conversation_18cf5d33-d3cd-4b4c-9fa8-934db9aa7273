"""Bookamat tool results processing node."""

import logging
from typing import Dict, Any

from ..state import BookamatState

logger = logging.getLogger(__name__)


def process_bookamat_tool_results_node(state: BookamatState) -> Dict[str, Any]:
    """
    Verarbeitet Tool-Ergebnisse im Bookamat Subgraph.

    Args:
        state: Aktueller BookamatState

    Returns:
        Aktualisierter State mit verarbeiteten Tool-Ergebnissen
    """
    try:
        # Get current messages and step
        messages = state.get("messages", [])
        current_step = state.get("current_step", 0)

        # For now, just increment step counter
        # Tool results are already processed in the task node

        logger.info(f"Processed Bookamat tool results, step {current_step}")

        return {
            "current_step": current_step + 1
        }

    except Exception as e:
        logger.error(f"Error processing Bookamat tool results: {e}")
        return {
            "current_step": state.get("current_step", 0) + 1,
            "error": f"Fehler beim Verarbeiten der Tool-Ergebnisse: {str(e)}"
        }
