"""
State-Management für den Bookamat Subgraph.
"""

from typing import TypedDict, Optional, List, Dict, Any, Annotated
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field

# Import from main agent state
from agent.state import ExecutionPlan, PlanTask


class BookingAmount(BaseModel):
    """Einzelner Betrag einer Buchung."""
    bankaccount: int = Field(description="ID des Zahlungsmittelkontos")
    costaccount: int = Field(description="ID des Steuerkontos")
    purchasetaxaccount: int = Field(description="ID des Umsatzsteuerkontos")
    amount: str = Field(description="Bruttobetrag als String (z.B. '1200.00')")
    tax_percent: str = Field(description="Umsatzsteuer in Prozent (z.B. '20.00')")
    deductibility_tax_percent: str = Field(default="100.00", description="Betrieblicher Anteil der Umsatzsteuer in %")
    deductibility_amount_percent: str = Field(default="100.00", description="Betrieblicher Anteil des Nettobetrags in %")
    foreign_business_base: Optional[int] = Field(default=None, description="ID der ausländischen Betriebsstätte")
    country_dep: str = Field(default="", description="Abgangsland")
    country_rec: str = Field(default="", description="Mitgliedstaat des Verbrauchs")


class BookingData(BaseModel):
    """Datenstruktur für eine Bookamat Buchung."""
    title: str = Field(description="Buchungstitel (max. 50 Zeichen)")
    date: Optional[str] = Field(default=None, description="Buchungsdatum im Format YYYY-MM-DD")
    date_invoice: Optional[str] = Field(default=None, description="Rechnungsdatum im Format YYYY-MM-DD")
    date_delivery: Optional[str] = Field(default=None, description="Lieferdatum im Format YYYY-MM-DD")
    date_order: Optional[str] = Field(default=None, description="Bestelldatum im Format YYYY-MM-DD")
    costcentre: Optional[int] = Field(default=None, description="ID der Kostenstelle")
    amounts: List[BookingAmount] = Field(description="Liste der Beträge (mindestens ein Eintrag erforderlich)")
    vatin: str = Field(default="", description="Umsatzsteuer-Identifikationsnummer")
    country: str = Field(default="", description="Land")
    description: str = Field(default="", description="Beschreibung (max. 500 Zeichen)")


class BookamatState(TypedDict):
    """State für den Bookamat Subgraph."""

    # Eingabe und Ausgabe
    input: str
    output: Optional[str]

    # Messages für LangGraph Integration
    messages: Annotated[List[BaseMessage], add_messages]

    # Planning system (like main agent)
    plan: Optional[ExecutionPlan]

    # Loop control
    current_step: Optional[int]
    max_steps: Optional[int]
    needs_action: Optional[bool]

    # Analysis and action results
    analysis: Optional[str]
    action_results: Optional[List[Dict[str, Any]]]

    # Bookamat-spezifische Daten
    booking_data: Optional[BookingData]
    booking_result: Optional[Dict[str, Any]]

    # Verfügbare Konten und Einstellungen (Cache)
    bank_accounts: Optional[List[Dict[str, Any]]]
    cost_accounts: Optional[List[Dict[str, Any]]]
    purchasetax_accounts: Optional[List[Dict[str, Any]]]
    cost_centres: Optional[List[Dict[str, Any]]]

    # Processing metadata
    timestamp: Optional[str]
    model_used: Optional[str]
    processing_time: Optional[float]

    # Fehlerbehandlung
    error: Optional[str]

    # Metadaten
    metadata: Optional[Dict[str, Any]]


def create_initial_bookamat_state(input_text: str) -> BookamatState:
    """
    Erstellt den initialen State für den Bookamat Subgraph.

    Args:
        input_text: Eingabetext mit der Buchungsanfrage

    Returns:
        Initialer BookamatState
    """
    from datetime import datetime

    return BookamatState(
        input=input_text,
        output=None,
        messages=[],
        plan=None,
        current_step=0,
        max_steps=5,
        needs_action=False,
        analysis=None,
        action_results=[],
        booking_data=None,
        booking_result=None,
        bank_accounts=None,
        cost_accounts=None,
        purchasetax_accounts=None,
        cost_centres=None,
        timestamp=datetime.now().isoformat(),
        model_used=None,
        processing_time=None,
        error=None,
        metadata={}
    )
