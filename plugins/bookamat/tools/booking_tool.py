"""Bookamat booking tool."""

import logging
from typing import Optional
from langchain_core.tools import tool
from datetime import datetime
from decimal import Decimal, InvalidOperation

from config.settings import settings
from ..client import BookamatClient, BookamatAPIError

logger = logging.getLogger(__name__)


def _get_bookamat_client() -> BookamatClient:
    """
    Erstellt einen Bookamat Client mit den konfigurierten Einstellungen.
    
    Returns:
        BookamatClient Instanz
        
    Raises:
        ValueError: Wenn die Bookamat Konfiguration unvollständig ist
    """
    if not settings.bookamat_username or not settings.bookamat_api_key:
        raise ValueError("Bookamat Benutzername und API-Key müssen in den Einstellungen konfiguriert sein.")
    
    return BookamatClient(
        username=settings.bookamat_username,
        api_key=settings.bookamat_api_key,
        country=settings.bookamat_country,
    )


@tool
def buchung_hinzufuegen(
    title: str,
    amount: str,
    bankaccount_id: int,
    costaccount_id: int,
    purchasetaxaccount_id: int,
    tax_percent: str = "20.00",
    date: Optional[str] = None,
    date_invoice: Optional[str] = None,
    description: str = "",
    costcentre_id: Optional[int] = None
) -> str:
    """
    Fügt eine neue Buchung in Bookamat hinzu.
    
    Diese Funktion erstellt eine neue Buchung in der Bookamat Buchhaltungssoftware.
    Alle Beträge müssen als String im Format "1234.56" angegeben werden.
    
    Args:
        title: Buchungstitel (max. 50 Zeichen)
        amount: Bruttobetrag als String (z.B. "1200.00")
        bankaccount_id: ID des Zahlungsmittelkontos
        costaccount_id: ID des Steuerkontos
        purchasetaxaccount_id: ID des Umsatzsteuerkontos
        tax_percent: Umsatzsteuer in Prozent als String (Standard: "20.00")
        date: Buchungsdatum im Format YYYY-MM-DD (optional, ohne Datum = offene Buchung)
        date_invoice: Rechnungsdatum im Format YYYY-MM-DD (optional)
        description: Beschreibung der Buchung (optional, max. 500 Zeichen)
        costcentre_id: ID der Kostenstelle (optional)
    
    Returns:
        Bestätigungsmeldung in deutscher Sprache mit Details der erstellten Buchung
    """
    try:
        # Validierung der Eingaben
        if not title or len(title) > 50:
            return "Fehler: Buchungstitel ist erforderlich und darf maximal 50 Zeichen lang sein."
        
        if description and len(description) > 500:
            return "Fehler: Beschreibung darf maximal 500 Zeichen lang sein."
        
        # Betrag validieren
        try:
            amount_decimal = Decimal(amount)
            if amount_decimal <= 0:
                return "Fehler: Der Betrag muss größer als 0 sein."
        except (InvalidOperation, ValueError):
            return "Fehler: Ungültiges Betragsformat. Verwenden Sie das Format '1234.56'."
        
        # Steuerprozentsatz validieren
        try:
            tax_decimal = Decimal(tax_percent)
            if tax_decimal < 0 or tax_decimal > 100:
                return "Fehler: Steuerprozentsatz muss zwischen 0 und 100 liegen."
        except (InvalidOperation, ValueError):
            return "Fehler: Ungültiger Steuerprozentsatz. Verwenden Sie das Format '20.00'."
        
        # Datum validieren (falls angegeben)
        if date:
            try:
                datetime.strptime(date, "%Y-%m-%d")
            except ValueError:
                return "Fehler: Ungültiges Datumsformat. Verwenden Sie YYYY-MM-DD."
        
        if date_invoice:
            try:
                datetime.strptime(date_invoice, "%Y-%m-%d")
            except ValueError:
                return "Fehler: Ungültiges Rechnungsdatumsformat. Verwenden Sie YYYY-MM-DD."
        
        # Bookamat Client erstellen
        client = _get_bookamat_client()
        
        # Buchungsdaten zusammenstellen
        booking_data = {
            "title": title,
            "amounts": [
                {
                    "bankaccount": bankaccount_id,
                    "costaccount": costaccount_id,
                    "purchasetaxaccount": purchasetaxaccount_id,
                    "amount": amount,
                    "tax_percent": tax_percent,
                    "deductibility_tax_percent": "100.00",
                    "deductibility_amount_percent": "100.00",
                    "foreign_business_base": None,
                    "country_dep": "",
                    "country_rec": ""
                }
            ],
            "description": description
        }
        
        # Optionale Felder hinzufügen
        if date:
            booking_data["date"] = date
        if date_invoice:
            booking_data["date_invoice"] = date_invoice
        if costcentre_id:
            booking_data["costcentre"] = costcentre_id
        
        # Buchung erstellen
        result = client.add_booking(booking_data)
        
        # Erfolgreiche Antwort formatieren
        booking_id = result.get("id")
        document_number = result.get("document_number", "N/A")
        status = result.get("status")
        
        status_text = {
            "1": "gebucht",
            "2": "offen",
            "3": "gelöscht",
            "4": "importiert"
        }.get(status, "unbekannt")
        
        response = f"""✅ Buchung erfolgreich erstellt!

📋 Details:
• Buchungs-ID: {booking_id}
• Belegnummer: {document_number}
• Titel: {title}
• Betrag: {amount} €
• Status: {status_text}
• Steuersatz: {tax_percent}%"""
        
        if date:
            response += f"\n• Buchungsdatum: {date}"
        if date_invoice:
            response += f"\n• Rechnungsdatum: {date_invoice}"
        if description:
            response += f"\n• Beschreibung: {description}"
        
        return response
        
    except BookamatAPIError as e:
        error_msg = f"Bookamat API Fehler: {str(e)}"
        logger.error(error_msg)
        return f"❌ Fehler beim Erstellen der Buchung: {str(e)}"
    
    except ValueError as e:
        error_msg = f"Konfigurationsfehler: {str(e)}"
        logger.error(error_msg)
        return f"❌ Konfigurationsfehler: {str(e)}"
    
    except Exception as e:
        error_msg = f"Unerwarteter Fehler beim Erstellen der Buchung: {str(e)}"
        logger.error(error_msg)
        return f"❌ Unerwarteter Fehler: {str(e)}"
