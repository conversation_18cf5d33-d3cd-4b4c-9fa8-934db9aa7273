"""Bookamat account information utilities."""

import logging
from config.settings import settings
from ..client import BookamatClient

logger = logging.getLogger(__name__)


def _get_bookamat_client() -> BookamatClient:
    """
    Erstellt einen Bookamat Client mit den konfigurierten Einstellungen.
    
    Returns:
        BookamatClient Instanz
        
    Raises:
        ValueError: Wenn die Bookamat Konfiguration unvollständig ist
    """
    if not settings.bookamat_username or not settings.bookamat_api_key:
        raise ValueError("Bookamat Benutzername und API-Key müssen in den Einstellungen konfiguriert sein.")
    
    return BookamatClient(
        username=settings.bookamat_username,
        api_key=settings.bookamat_api_key,
        country=settings.bookamat_country,
    )


def get_account_information() -> str:
    """
    Ruft alle verfügbaren Kontoinformationen aus Bookamat ab.
    Diese Funktion wird intern verwendet, um Kontoinformationen für die Systemanweisung zu sammeln.

    Returns:
        Formatierte Liste aller verfügbaren Konten
    """
    try:
        client = _get_bookamat_client()

        result_parts = ["=== VERFÜGBARE BOOKAMAT KONTEN ==="]

        # Zahlungsmittelkonten
        try:
            bank_accounts = client.get_bank_accounts()
            if bank_accounts.get("results"):
                result_parts.append("\n💳 ZAHLUNGSMITTELKONTEN:")
                for account in bank_accounts["results"]:
                    result_parts.append(f"  • ID: {account['id']} - {account['name']}")
        except Exception as e:
            result_parts.append(f"\n⚠️ Fehler beim Abrufen der Bankkonten: {str(e)}")

        # Steuerkonten
        try:
            cost_accounts = client.get_cost_accounts()
            if cost_accounts.get("results"):
                result_parts.append("\n📊 STEUERKONTEN:")
                for account in cost_accounts["results"]:
                    result_parts.append(f"  • ID: {account['id']} - {account['name']}")
        except Exception as e:
            result_parts.append(f"\n⚠️ Fehler beim Abrufen der Steuerkonten: {str(e)}")

        # Umsatzsteuerkonten
        try:
            tax_accounts = client.get_purchasetax_accounts()
            if tax_accounts.get("results"):
                result_parts.append("\n🧾 UMSATZSTEUERKONTEN:")
                for account in tax_accounts["results"]:
                    result_parts.append(f"  • ID: {account['id']} - {account['name']}")
        except Exception as e:
            result_parts.append(f"\n⚠️ Fehler beim Abrufen der Umsatzsteuerkonten: {str(e)}")

        # Kostenstellen
        try:
            cost_centres = client.get_cost_centres()
            if cost_centres.get("results"):
                result_parts.append("\n🏢 KOSTENSTELLEN:")
                for centre in cost_centres["results"]:
                    result_parts.append(f"  • ID: {centre['id']} - {centre['name']}")
        except Exception as e:
            result_parts.append(f"\n⚠️ Fehler beim Abrufen der Kostenstellen: {str(e)}")

        result_parts.append("\n" + "="*50)

        return "\n".join(result_parts)

    except ValueError as e:
        return f"❌ Konfigurationsfehler: {str(e)}"
    except Exception as e:
        error_msg = f"Fehler beim Abrufen der Konten: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
