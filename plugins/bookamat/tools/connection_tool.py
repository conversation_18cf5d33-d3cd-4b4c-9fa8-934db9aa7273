"""Bookamat connection test tool."""

import logging
from langchain_core.tools import tool

from config.settings import settings
from ..client import BookamatClient

logger = logging.getLogger(__name__)


def _get_bookamat_client() -> BookamatClient:
    """
    Erstellt einen Bookamat Client mit den konfigurierten Einstellungen.
    
    Returns:
        BookamatClient Instanz
        
    Raises:
        ValueError: Wenn die Bookamat Konfiguration unvollständig ist
    """
    if not settings.bookamat_username or not settings.bookamat_api_key:
        raise ValueError("Bookamat Benutzername und API-Key müssen in den Einstellungen konfiguriert sein.")
    
    return BookamatClient(
        username=settings.bookamat_username,
        api_key=settings.bookamat_api_key,
        country=settings.bookamat_country,
    )


@tool
def bookamat_verbindung_testen() -> str:
    """
    Testet die Verbindung zur Bookamat API.
    
    Returns:
        Status der Verbindung in deutscher Sprache
    """
    try:
        client = _get_bookamat_client()
        
        if client.test_connection():
            return "✅ Verbindung zur Bookamat API erfolgreich!"
        else:
            return "❌ Verbindung zur Bookamat API fehlgeschlagen."
            
    except ValueError as e:
        return f"❌ Konfigurationsfehler: {str(e)}"
    except Exception as e:
        error_msg = f"Fehler beim Testen der Verbindung: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
