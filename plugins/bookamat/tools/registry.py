"""Bookamat tools registry."""

from .booking_tool import buchung_hinzufuegen
from .connection_tool import bookamat_verbindung_testen
from .account_info import get_account_information
from agent.tools import get_shared_tools

# List of Bookamat-specific tools (only for internal use in the subgraph)
BOOKAMAT_SPECIFIC_TOOLS = [
    buchung_hinzufuegen,
    bookamat_verbindung_testen
]

# Combined tools: Bookamat-specific + shared tools from main agent
def get_bookamat_tools():
    """Get all tools available to the Bookamat subgraph."""
    shared_tools = get_shared_tools()  # Get shared tools from main agent
    return BOOKAMAT_SPECIFIC_TOOLS + shared_tools

# For backward compatibility
BOOKAMAT_TOOLS = get_bookamat_tools()

# Tool descriptions for dynamic prompt generation
BOOKAMAT_TOOL_DESCRIPTIONS = {
    "buchung_hinzufuegen": {
        "name": "buchung_hinzufuegen",
        "description": "Fügt eine neue Buchung in Bookamat hinzu. Diese Funktion erstellt eine neue Buchung in der Bookamat Buchhaltungssoftware. Alle Beträge müssen als String im Format '1234.56' angegeben werden.",
        "category": "accounting"
    },
    "bookamat_verbindung_testen": {
        "name": "bookamat_verbindung_testen",
        "description": "Testet die Verbindung zur Bookamat API.",
        "category": "system"
    }
}

def get_bookamat_tools_description() -> str:
    """Generate a formatted description of all available Bookamat tools for system prompts."""
    from shared.tools_registry import tools_registry

    # Get shared tools description
    shared_description = tools_registry.get_tools_description_text(
        categories=["communication", "file_management", "file_processing"]
    )

    # Add Bookamat-specific tools
    bookamat_parts = ["### Bookamat-Specific Tools\n"]

    categories = {}
    for tool_name, tool_info in BOOKAMAT_TOOL_DESCRIPTIONS.items():
        category = tool_info["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append(tool_info)

    for category, tools in categories.items():
        bookamat_parts.append(f"#### {category.title()} Tools")
        for tool in tools:
            bookamat_parts.append(f"**{tool['name']}**: {tool['description']}")
        bookamat_parts.append("")

    bookamat_description = "\n".join(bookamat_parts)

    return f"{shared_description}\n{bookamat_description}"
