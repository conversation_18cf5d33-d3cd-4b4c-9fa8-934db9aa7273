"""
Nodes für den Bookamat Subgraph.
"""

import logging
import time
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import BookamatState
from agent.state import ExecutionPlan, PlanTask
from .client import BookamatClient, BookamatAPIError
from .tools import get_bookamat_tools, get_account_information
from config.settings import settings

logger = logging.getLogger(__name__)


class BookamatPlan(BaseModel):
    """Ausführungsplan für Bookamat-Operationen."""
    plan_summary: str = Field(description="1-Satz Zusammenfassung des Plans")
    tasks: list[PlanTask] = Field(description="Liste der auszuführenden Aufgaben")
    clarification_needed: list[str] = Field(default_factory=list, description="Benötigte Klarstellungen")
    current_task_index: int = Field(default=0, description="Index der aktuellen Aufgabe")


def bookamat_planning_node(state: BookamatState) -> BookamatState:
    """
    Erstellt einen Ausführungsplan für die Bookamat-Anfrage.

    Args:
        state: Aktueller BookamatState

    Returns:
        Aktualisierter BookamatState mit Ausführungsplan
    """
    try:
        start_time = time.time()

        # Get account information for system message
        account_info = get_account_information()

        # Create LLM for planning
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=settings.agent_temperature,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        # System message with account information
        system_message = SystemMessage(content=f"""Du bist ein Experte für Bookamat Buchhaltungssoftware.
Erstelle einen detaillierten Ausführungsplan für die gegebene Anfrage.

{account_info}

Verfügbare Tools:
- buchung_hinzufuegen: Erstellt eine neue Buchung in Bookamat
- bookamat_verbindung_testen: Testet die API-Verbindung

Erstelle einen Plan mit konkreten, ausführbaren Aufgaben. Jede Aufgabe sollte:
- Eine klare Aktion beschreiben (mit Imperativ-Verb beginnen)
- Das erwartete Ergebnis definieren
- Alle notwendigen Parameter identifizieren

Antworte auf Deutsch.""")

        # User message with the request
        user_message = HumanMessage(content=f"""
Erstelle einen Ausführungsplan für folgende Anfrage:

{state['input']}

Analysiere die Anfrage und erstelle einen strukturierten Plan mit konkreten Aufgaben.
""")

        # Get planning response
        response = llm.with_structured_output(BookamatPlan).invoke([system_message, user_message])

        processing_time = time.time() - start_time

        # Convert to ExecutionPlan format
        execution_plan = ExecutionPlan(
            plan_summary=response.plan_summary,
            tasks=response.tasks,
            clarification_needed=response.clarification_needed,
            current_task_index=0
        )

        logger.info(f"Created Bookamat plan with {len(execution_plan.tasks)} tasks")

        return {
            **state,
            "plan": execution_plan,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "timestamp": time.time()
        }

    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Fehler bei der Bookamat Planung: {str(e)}"
        logger.error(error_msg)

        return {
            **state,
            "error": error_msg,
            "processing_time": processing_time
        }


def bookamat_task_node(state: BookamatState) -> BookamatState:
    """
    Führt die nächste Aufgabe im Bookamat-Plan aus.

    Args:
        state: Aktueller BookamatState

    Returns:
        Aktualisierter BookamatState mit Aufgaben-Ergebnissen
    """
    try:
        start_time = time.time()

        # Get current plan and task
        plan = state.get("plan")
        if not plan or not plan.tasks:
            return {
                **state,
                "error": "Kein Ausführungsplan verfügbar",
                "current_step": state.get("current_step", 0) + 1
            }

        current_task_index = plan.current_task_index
        if current_task_index >= len(plan.tasks):
            return {
                **state,
                "error": "Alle Aufgaben bereits abgeschlossen",
                "current_step": state.get("current_step", 0) + 1
            }

        current_task = plan.tasks[current_task_index]
        current_step = state.get("current_step", 0)

        logger.info(f"Executing Bookamat task {current_task.id}: {current_task.description}")

        # Create LLM with tools
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.1,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        llm_with_tools = llm.bind_tools(get_bookamat_tools())

        # Get current messages
        messages = state.get("messages", [])

        # Create task message
        task_message = HumanMessage(content=f"""
Führe folgende Aufgabe aus:

Aufgabe {current_task.id}: {current_task.description}
Erwartetes Ergebnis: {current_task.deliverable}

Verwende die verfügbaren Bookamat-Tools, um diese Aufgabe zu erfüllen.
Antworte auf Deutsch.
        """)

        # Execute task with tools
        response = llm_with_tools.invoke(messages + [task_message])

        processing_time = time.time() - start_time

        # Mark current task as completed and move to next
        plan.tasks[current_task_index].completed = True
        plan.current_task_index += 1

        # Store the action result
        action_results = state.get("action_results", [])
        action_results.append({
            "task_id": current_task.id,
            "task_description": current_task.description,
            "response": response.content if response.content else "Tool-Aufruf ausgeführt",
            "processing_time": processing_time
        })

        logger.info(f"Completed Bookamat task {current_task.id}: {current_task.description}")

        # Check if there are more tasks to execute
        has_more_tasks = plan.current_task_index < len(plan.tasks)

        return {
            **state,
            "plan": plan,
            "action_results": action_results,
            "current_step": current_step + 1,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "messages": [response],  # Add response for tool processing
            "needs_action": has_more_tasks  # Continue if more tasks
        }

    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Fehler bei der Bookamat Aufgabenausführung: {str(e)}"
        logger.error(error_msg)

        return {
            **state,
            "error": error_msg,
            "processing_time": processing_time,
            "current_step": state.get("current_step", 0) + 1
        }


def bookamat_format_response_node(state: BookamatState) -> BookamatState:
    """
    Formatiert die finale Antwort für den Benutzer basierend auf den Ausführungsergebnissen.

    Args:
        state: Aktueller BookamatState

    Returns:
        BookamatState mit formatierter Ausgabe
    """
    try:
        start_time = time.time()

        # Check for errors first
        if state.get("error"):
            final_output = f"❌ Fehler: {state['error']}"
        else:
            # Get action results
            action_results = state.get("action_results", [])
            plan = state.get("plan")

            if action_results:
                # Format results from completed tasks
                output_parts = ["✅ Bookamat-Operationen abgeschlossen:\n"]

                for result in action_results:
                    output_parts.append(f"📋 {result['task_description']}")
                    if result['response']:
                        output_parts.append(f"   Ergebnis: {result['response']}")
                    output_parts.append("")

                # Add plan summary if available
                if plan and plan.plan_summary:
                    output_parts.insert(1, f"🎯 Plan: {plan.plan_summary}\n")

                final_output = "\n".join(output_parts).strip()

            elif plan and plan.clarification_needed:
                # Need clarification
                final_output = "❓ Für die Ausführung werden weitere Informationen benötigt:\n\n"
                for i, clarification in enumerate(plan.clarification_needed, 1):
                    final_output += f"{i}. {clarification}\n"
                final_output += "\nBitte geben Sie die fehlenden Informationen an."

            else:
                # No specific results
                final_output = "ℹ️ Bookamat-Anfrage verarbeitet, aber keine spezifischen Ergebnisse generiert."

        processing_time = time.time() - start_time

        # Create final response message
        response_message = AIMessage(content=final_output)

        return {
            **state,
            "messages": state["messages"] + [response_message],
            "output": final_output,
            "processing_time": processing_time
        }

    except Exception as e:
        error_msg = f"Fehler bei der Antwort-Formatierung: {str(e)}"
        logger.error(error_msg)

        return {
            **state,
            "error": error_msg,
            "output": f"❌ Antwort-Fehler: {error_msg}"
        }


def process_bookamat_tool_results_node(state: BookamatState) -> Dict[str, Any]:
    """
    Verarbeitet Tool-Ergebnisse im Bookamat Subgraph.

    Args:
        state: Aktueller BookamatState

    Returns:
        Aktualisierter State mit verarbeiteten Tool-Ergebnissen
    """
    try:
        # Get current messages and step
        messages = state.get("messages", [])
        current_step = state.get("current_step", 0)

        # For now, just increment step counter
        # Tool results are already processed in the task node

        logger.info(f"Processed Bookamat tool results, step {current_step}")

        return {
            "current_step": current_step + 1
        }

    except Exception as e:
        logger.error(f"Error processing Bookamat tool results: {e}")
        return {
            "current_step": state.get("current_step", 0) + 1,
            "error": f"Fehler beim Verarbeiten der Tool-Ergebnisse: {str(e)}"
        }



