"""
Bookamat Subgraph für die LangGraph Integration.
"""

import logging
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode, tools_condition

from .state import BookamatState, create_initial_bookamat_state
from .nodes import (
    bookamat_planning_node,
    bookamat_task_node,
    bookamat_format_response_node,
    process_bookamat_tool_results_node
)
from .tools import BOOKAMAT_TOOLS

logger = logging.getLogger(__name__)


def should_continue_after_planning(state: BookamatState) -> str:
    """
    Bestimmt den nächsten Schritt nach der Planung.

    Args:
        state: Aktueller BookamatState

    Returns:
        Name des nächsten Knotens
    """
    # Fehler-Behandlung
    if state.get("error"):
        return "format_response"

    # Prüfen ob ein Plan erstellt wurde
    plan = state.get("plan")
    if not plan or not plan.tasks:
        return "format_response"

    # Prüfen ob Klarstellungen benötigt werden
    if plan.clarification_needed:
        return "format_response"  # Return with clarification questions

    # Start task execution
    return "task_node"


def should_continue_after_task(state: BookamatState) -> str:
    """
    Bestimmt den nächsten Schritt nach der Aufgabenausführung.

    Args:
        state: Aktueller BookamatState

    Returns:
        Name des nächsten Knotens
    """
    # Fehler-Behandlung
    if state.get("error"):
        return "format_response"

    # Prüfen ob maximale Schritte erreicht wurden
    current_step = state.get("current_step", 0)
    max_steps = state.get("max_steps", 5)

    if current_step >= max_steps:
        return "format_response"

    # Prüfen ob weitere Aufgaben ausgeführt werden müssen
    needs_action = state.get("needs_action", False)

    if needs_action:
        return "task_node"  # Continue with next task
    else:
        return "format_response"  # All tasks completed


def create_bookamat_subgraph():
    """
    Erstellt den Bookamat Subgraph nach dem Muster des Hauptgraphen.

    Returns:
        Kompilierter LangGraph für Bookamat Operationen
    """
    # StateGraph erstellen
    workflow = StateGraph(BookamatState)

    # Knoten hinzufügen (wie im Hauptgraphen)
    workflow.add_node("planning", bookamat_planning_node)
    workflow.add_node("task_node", bookamat_task_node)
    workflow.add_node("tools", ToolNode(BOOKAMAT_TOOLS))
    workflow.add_node("process_tool_results", process_bookamat_tool_results_node)
    workflow.add_node("format_response", bookamat_format_response_node)

    # Einstiegspunkt setzen
    workflow.set_entry_point("planning")

    # Bedingte Kante von planning
    workflow.add_conditional_edges(
        "planning",
        should_continue_after_planning,
        {
            "task_node": "task_node",
            "format_response": "format_response"
        }
    )

    # Bedingte Kante von task_node für Tool-Aufrufe
    workflow.add_conditional_edges(
        "task_node",
        tools_condition,
        {
            "tools": "tools",
            "__end__": "process_tool_results"
        }
    )

    # Bedingte Kante von process_tool_results
    workflow.add_conditional_edges(
        "process_tool_results",
        should_continue_after_task,
        {
            "task_node": "task_node",
            "format_response": "format_response"
        }
    )

    # Kante von tools zurück zu process_tool_results
    workflow.add_edge("tools", "process_tool_results")

    # Finale Kante zum Ende
    workflow.add_edge("format_response", END)

    # Memory für State-Persistierung
    memory = MemorySaver()

    # Graph kompilieren
    app = workflow.compile(checkpointer=memory)

    return app


def run_bookamat_subgraph(input_text: str, thread_id: str = "bookamat_default") -> dict:
    """
    Führt den Bookamat Subgraph aus.
    
    Args:
        input_text: Eingabetext mit der Bookamat-Anfrage
        thread_id: Thread-ID für die Konversation
        
    Returns:
        Ergebnis der Bookamat-Operation
    """
    try:
        # Subgraph erstellen
        app = create_bookamat_subgraph()
        
        # Initialen State erstellen
        initial_state = create_initial_bookamat_state(input_text)
        initial_state.update({
            "current_step": 0,
            "max_steps": 3,  # Reduced to 3 steps to prevent loops
            "needs_action": False
        })

        # Konfiguration für Thread
        config = {"configurable": {"thread_id": thread_id}}

        # Subgraph ausführen
        result = app.invoke(initial_state, config=config)
        
        return {
            "success": True,
            "output": result.get("output", "Keine Ausgabe generiert"),
            "error": result.get("error"),
            "metadata": result.get("metadata", {}),
            "thread_id": thread_id
        }
        
    except Exception as e:
        error_msg = f"Fehler beim Ausführen des Bookamat Subgraphs: {str(e)}"
        logger.error(error_msg)
        
        return {
            "success": False,
            "output": None,
            "error": error_msg,
            "metadata": {},
            "thread_id": thread_id
        }


# Beispiel für die Verwendung
if __name__ == "__main__":
    # Test des Subgraphs
    test_inputs = [
        "Teste die Verbindung zu Bookamat",
        "Liste alle verfügbaren Konten auf",
        "Erstelle eine neue Buchung für Büroausstattung"
    ]
    
    for test_input in test_inputs:
        print(f"\n--- Test: {test_input} ---")
        result = run_bookamat_subgraph(test_input)
        print(f"Erfolg: {result['success']}")
        print(f"Ausgabe: {result['output']}")
        if result['error']:
            print(f"Fehler: {result['error']}")
        print(f"Metadaten: {result['metadata']}")
