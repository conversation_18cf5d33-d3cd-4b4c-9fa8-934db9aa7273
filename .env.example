# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# LangSmith Configuration (optional but recommended for monitoring)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=langgraph-agent

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=true
FLASK_PORT=5000

# Agent Configuration
AGENT_MODEL=gpt-3.5-turbo
AGENT_TEMPERATURE=0.7
AGENT_MAX_TOKENS=1000

# Email Configuration
# Gmail Example:
EMAIL_IMAP_SERVER=imap.gmail.com
EMAIL_IMAP_PORT=993
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Outlook Example:
# EMAIL_IMAP_SERVER=outlook.office365.com
# EMAIL_IMAP_PORT=993
# EMAIL_SMTP_SERVER=smtp-mail.outlook.com
# EMAIL_SMTP_PORT=587
# EMAIL_USERNAME=<EMAIL>
# EMAIL_PASSWORD=your_password

# Yahoo Example:
# EMAIL_IMAP_SERVER=imap.mail.yahoo.com
# EMAIL_IMAP_PORT=993
# EMAIL_SMTP_SERVER=smtp.mail.yahoo.com
# EMAIL_SMTP_PORT=587

# Email Credentials
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password-here

# Email Settings
EMAIL_USE_SSL=true
EMAIL_USE_TLS=true
EMAIL_CHECK_INTERVAL=60
EMAIL_INBOX_FOLDER=INBOX

# Plugins

# Bookamat
BOOKAMAT_API_KEY=userName:apiKey