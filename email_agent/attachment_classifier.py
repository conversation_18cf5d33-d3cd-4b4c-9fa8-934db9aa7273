"""Attachment classification using OpenAI to identify relevant files."""

import logging
from typing import List, Dict, Any
from langchain_openai import Chat<PERSON>penAI
from langchain.schema import HumanMessage, SystemMessage

from config.settings import settings

logger = logging.getLogger(__name__)


class AttachmentClassifier:
    """Classifies email attachments to identify relevant files vs. logos/signatures."""
    
    def __init__(self):
        """Initialize the classifier with OpenAI client."""
        self.llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.1,  # Low temperature for consistent classification
            max_tokens=100,
            openai_api_key=settings.openai_api_key
        )
    
    def classify_attachments(self, attachments: List[Dict[str, Any]], email_subject: str, email_body: str) -> List[Dict[str, Any]]:
        """Classify attachments as relevant or not.
        
        Args:
            attachments: List of attachment metadata
            email_subject: Email subject for context
            email_body: Email body for context
            
        Returns:
            List of attachments with classification results
        """
        if not attachments:
            return []
        
        classified_attachments = []
        
        for attachment in attachments:
            try:
                is_relevant = self._classify_single_attachment(
                    attachment, email_subject, email_body
                )
                
                attachment_copy = attachment.copy()
                attachment_copy['is_relevant'] = is_relevant
                attachment_copy['classification_reason'] = self._get_classification_reason(
                    attachment, is_relevant
                )
                
                classified_attachments.append(attachment_copy)
                
                logger.info(f"Classified attachment {attachment['filename']}: {'relevant' if is_relevant else 'not relevant'}")
                
            except Exception as e:
                logger.error(f"Failed to classify attachment {attachment['filename']}: {e}")
                # Default to relevant if classification fails
                attachment_copy = attachment.copy()
                attachment_copy['is_relevant'] = True
                attachment_copy['classification_reason'] = "Classification failed - defaulted to relevant"
                classified_attachments.append(attachment_copy)
        
        return classified_attachments
    
    def _classify_single_attachment(self, attachment: Dict[str, Any], email_subject: str, email_body: str) -> bool:
        """Classify a single attachment.
        
        Args:
            attachment: Attachment metadata
            email_subject: Email subject
            email_body: Email body
            
        Returns:
            True if attachment is relevant, False otherwise
        """
        filename = attachment.get('filename', '')
        content_type = attachment.get('content_type', '')
        file_size = attachment.get('file_size', 0)
        
        # Quick heuristic checks first
        if self._is_likely_signature_image(filename, content_type, file_size):
            return False
        
        # Use OpenAI for more complex classification
        system_prompt = """Du bist ein Experte für die Klassifikation von E-Mail-Anhängen. 
Deine Aufgabe ist es zu bestimmen, ob ein Anhang relevant für den Benutzer ist oder nur ein Logo/Signatur-Element.

Klassifiziere als NICHT RELEVANT:
- Kleine Bilder (< 50KB) die wahrscheinlich Logos oder Signaturen sind
- Dateien mit Namen wie "logo", "signature", "banner", "header"
- Sehr kleine Dateien die nur zur Darstellung dienen
- Tracking-Pixel oder ähnliche Marketing-Elemente

Klassifiziere als RELEVANT:
- Dokumente (PDF, DOC, XLS, etc.)
- Große Bilder oder Screenshots
- Dateien die im E-Mail-Text erwähnt werden
- Alles was wie echte Arbeitsdateien aussieht

Antworte nur mit "RELEVANT" oder "NICHT_RELEVANT"."""

        user_prompt = f"""E-Mail Betreff: {email_subject}

E-Mail Inhalt (erste 500 Zeichen): {email_body[:500]}

Anhang Details:
- Dateiname: {filename}
- Content-Type: {content_type}
- Dateigröße: {file_size} Bytes

Ist dieser Anhang relevant für den Benutzer?"""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            result = response.content.strip().upper()
            
            return "RELEVANT" in result
            
        except Exception as e:
            logger.error(f"OpenAI classification failed: {e}")
            # Default to relevant if OpenAI fails
            return True
    
    def _is_likely_signature_image(self, filename: str, content_type: str, file_size: int) -> bool:
        """Quick heuristic check for signature images.
        
        Args:
            filename: File name
            content_type: MIME content type
            file_size: File size in bytes
            
        Returns:
            True if likely a signature image
        """
        filename_lower = filename.lower()
        
        # Check for signature-related keywords
        signature_keywords = [
            'logo', 'signature', 'sig', 'banner', 'header', 'footer',
            'brand', 'company', 'corp', 'inc', 'ltd', 'gmbh'
        ]
        
        for keyword in signature_keywords:
            if keyword in filename_lower:
                return True
        
        # Check if it's a small image (likely logo/signature)
        if content_type.startswith('image/') and file_size < 50000:  # < 50KB
            return True
        
        return False
    
    def _get_classification_reason(self, attachment: Dict[str, Any], is_relevant: bool) -> str:
        """Get human-readable reason for classification.
        
        Args:
            attachment: Attachment metadata
            is_relevant: Classification result
            
        Returns:
            Reason string
        """
        filename = attachment.get('filename', '')
        content_type = attachment.get('content_type', '')
        file_size = attachment.get('file_size', 0)
        
        if not is_relevant:
            if self._is_likely_signature_image(filename, content_type, file_size):
                return "Likely signature/logo image based on filename and size"
            else:
                return "Classified as non-relevant by AI analysis"
        else:
            if content_type.startswith('application/'):
                return "Document file - likely relevant"
            elif file_size > 100000:  # > 100KB
                return "Large file - likely relevant content"
            else:
                return "Classified as relevant by AI analysis"
