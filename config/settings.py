"""Configuration settings for the LangGraph agent."""

import os
from typing import Optional
from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """Application settings."""
    
    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    agent_model: str = Field(default="gpt-4.1-mini", env="AGENT_MODEL")
    agent_temperature: float = Field(default=0.7, env="AGENT_TEMPERATURE")
    agent_max_tokens: int = Field(default=1000, env="AGENT_MAX_TOKENS")
    
    # LangSmith Configuration
    langchain_tracing_v2: bool = Field(default=True, env="LANGCHAIN_TRACING_V2")
    langchain_endpoint: str = Field(
        default="https://api.smith.langchain.com", 
        env="LANGCHAIN_ENDPOINT"
    )
    langchain_api_key: Optional[str] = Field(default=None, env="LANGCHAIN_API_KEY")
    langchain_project: str = Field(default="langgraph-agent", env="LANGCHAIN_PROJECT")
    
    # Flask Configuration
    flask_env: str = Field(default="development", env="FLASK_ENV")
    flask_debug: bool = Field(default=True, env="FLASK_DEBUG")
    flask_port: int = Field(default=5000, env="FLASK_PORT")

    # Email Configuration (optional for basic functionality)
    email_imap_server: Optional[str] = Field(default=None, env="EMAIL_IMAP_SERVER")
    email_imap_port: int = Field(default=993, env="EMAIL_IMAP_PORT")
    email_smtp_server: Optional[str] = Field(default=None, env="EMAIL_SMTP_SERVER")
    email_smtp_port: int = Field(default=587, env="EMAIL_SMTP_PORT")
    email_username: Optional[str] = Field(default=None, env="EMAIL_USERNAME")
    email_password: Optional[str] = Field(default=None, env="EMAIL_PASSWORD")
    email_use_ssl: bool = Field(default=True, env="EMAIL_USE_SSL")
    email_use_tls: bool = Field(default=True, env="EMAIL_USE_TLS")
    email_check_interval: int = Field(default=60, env="EMAIL_CHECK_INTERVAL")  # seconds
    email_inbox_folder: str = Field(default="INBOX", env="EMAIL_INBOX_FOLDER")

    # Bookamat Configuration
    bookamat_username: Optional[str] = Field(default=None, env="BOOKAMAT_USERNAME")
    bookamat_api_key: Optional[str] = Field(default=None, env="BOOKAMAT_API_KEY")
    bookamat_country: str = Field(default="at", env="BOOKAMAT_COUNTRY")
    bookamat_year: Optional[int] = Field(default=None, env="BOOKAMAT_YEAR")

    # Accounting Software Configuration (for subgraph selection)
    accounting_software: str = Field(default="bookamat", env="ACCOUNTING_SOFTWARE")

    class Config:
        env_file = ".env"
        case_sensitive = False


def get_settings() -> Settings:
    """Get application settings."""
    return Settings()


def setup_langsmith():
    """Setup LangSmith tracing if configured."""
    settings = get_settings()
    
    if settings.langchain_api_key:
        os.environ["LANGCHAIN_TRACING_V2"] = str(settings.langchain_tracing_v2).lower()
        os.environ["LANGCHAIN_ENDPOINT"] = settings.langchain_endpoint
        os.environ["LANGCHAIN_API_KEY"] = settings.langchain_api_key
        os.environ["LANGCHAIN_PROJECT"] = settings.langchain_project
        print(f"✅ LangSmith tracing enabled for project: {settings.langchain_project}")
    else:
        print("⚠️  LangSmith API key not found. Tracing disabled.")


# Initialize settings
settings = get_settings()

# Setup LangSmith on import
setup_langsmith()
