"""Tests for email attachment processing functionality."""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

from email_agent.connection import EmailConnection
from email_agent.database import EmailDatabase
from email_agent.attachment_classifier import AttachmentClassifier


class TestAttachmentProcessing(unittest.TestCase):
    """Test cases for attachment processing functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock settings
        self.mock_settings = Mock()
        self.mock_settings.email_username = "<EMAIL>"
        self.mock_settings.email_password = "password"
        self.mock_settings.email_imap_server = "imap.example.com"
        self.mock_settings.email_imap_port = 993
        self.mock_settings.email_smtp_server = "smtp.example.com"
        self.mock_settings.email_smtp_port = 587
        self.mock_settings.email_inbox_folder = "INBOX"
        self.mock_settings.agent_model = "gpt-3.5-turbo"
        self.mock_settings.openai_api_key = "test-key"
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def create_test_email_with_attachment(self):
        """Create a test email with attachment."""
        msg = MIMEMultipart()
        msg['From'] = "<EMAIL>"
        msg['To'] = "<EMAIL>"
        msg['Subject'] = "Test email with attachment"
        
        # Add body
        body = "This is a test email with an attachment."
        msg.attach(MIMEText(body, 'plain'))
        
        # Add attachment
        attachment_content = b"This is test file content"
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(attachment_content)
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            'attachment; filename="test_document.pdf"'
        )
        msg.attach(part)
        
        return msg
    
    def create_test_email_with_logo(self):
        """Create a test email with logo/signature image."""
        msg = MIMEMultipart()
        msg['From'] = "<EMAIL>"
        msg['To'] = "<EMAIL>"
        msg['Subject'] = "Test email with logo"
        
        # Add body
        body = "This is a test email with a company logo."
        msg.attach(MIMEText(body, 'plain'))
        
        # Add small logo image
        logo_content = b"fake_logo_content"
        part = MIMEBase('image', 'png')
        part.set_payload(logo_content)
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            'attachment; filename="company_logo.png"'
        )
        msg.attach(part)
        
        return msg
    
    @patch('email_agent.connection.settings')
    def test_extract_attachments_basic(self, mock_settings):
        """Test basic attachment extraction."""
        mock_settings.return_value = self.mock_settings
        
        # Create connection with mocked tmp directory
        connection = EmailConnection()
        connection.tmp_dir = self.temp_dir
        
        # Mock the classifier to avoid OpenAI calls
        connection.attachment_classifier = Mock()
        connection.attachment_classifier.classify_attachments.return_value = [
            {
                'filename': 'test_document.pdf',
                'safe_filename': 'abc12345_test_document.pdf',
                'file_path': os.path.join(self.temp_dir, 'email_123', 'abc12345_test_document.pdf'),
                'content_type': 'application/octet-stream',
                'file_size': 24,
                'unique_id': 'abc12345',
                'email_uid': '123',
                'is_relevant': True,
                'classification_reason': 'Document file - likely relevant'
            }
        ]
        
        # Create test email
        test_email = self.create_test_email_with_attachment()
        
        # Extract attachments
        attachments = connection._extract_attachments(
            test_email, '123', 'Test subject', 'Test body'
        )
        
        # Verify results
        self.assertEqual(len(attachments), 1)
        self.assertEqual(attachments[0]['filename'], 'test_document.pdf')
        self.assertTrue(attachments[0]['is_relevant'])
    
    @patch('email_agent.attachment_classifier.ChatOpenAI')
    def test_attachment_classifier(self, mock_openai):
        """Test attachment classification."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.content = "RELEVANT"
        mock_openai.return_value.invoke.return_value = mock_response
        
        classifier = AttachmentClassifier()
        
        # Test document attachment
        attachments = [{
            'filename': 'important_document.pdf',
            'content_type': 'application/pdf',
            'file_size': 500000
        }]
        
        result = classifier.classify_attachments(
            attachments, "Important document", "Please find attached document"
        )
        
        self.assertEqual(len(result), 1)
        self.assertTrue(result[0]['is_relevant'])
    
    def test_database_attachment_operations(self):
        """Test database operations for attachments."""
        # Create temporary database
        db_path = os.path.join(self.temp_dir, 'test.db')
        database = EmailDatabase(db_path)
        database.init_database()
        
        # Test data
        email_uid = "test_email_123"
        attachments = [{
            'filename': 'test.pdf',
            'safe_filename': 'abc123_test.pdf',
            'file_path': '/tmp/test.pdf',
            'content_type': 'application/pdf',
            'file_size': 1024,
            'unique_id': 'abc123',
            'is_relevant': True,
            'classification_reason': 'Document file'
        }]
        
        # Save attachments
        database.save_email_attachments(email_uid, attachments)
        
        # Retrieve attachments
        retrieved = database.get_email_attachments(email_uid)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0]['filename'], 'test.pdf')
        
        # Get by ID
        attachment = database.get_attachment_by_id('abc123')
        self.assertIsNotNone(attachment)
        self.assertEqual(attachment['filename'], 'test.pdf')


if __name__ == '__main__':
    unittest.main()
