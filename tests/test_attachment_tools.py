"""Tests for attachment-related agent tools."""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch

from agent.tools import get_file_attachment, list_email_attachments, process_file_with_llm
from email_agent.database import EmailDatabase


class TestAttachmentTools(unittest.TestCase):
    """Test cases for attachment tools."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, 'test.db')
        
        # Create test database with sample data
        self.database = EmailDatabase(self.db_path)
        self.database.init_database()
        
        # Add test attachment
        test_file_path = os.path.join(self.temp_dir, 'test_document.pdf')
        with open(test_file_path, 'w') as f:
            f.write("Test document content")
        
        attachments = [{
            'filename': 'test_document.pdf',
            'safe_filename': 'abc123_test_document.pdf',
            'file_path': test_file_path,
            'content_type': 'application/pdf',
            'file_size': 20,
            'unique_id': 'abc123',
            'is_relevant': True,
            'classification_reason': 'Document file - likely relevant'
        }]
        
        self.database.save_email_attachments('test_email_123', attachments)
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @patch('agent.tools.EmailDatabase')
    def test_get_file_attachment_success(self, mock_db_class):
        """Test successful file attachment retrieval."""
        # Mock database
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        
        # Create test file
        test_file_path = os.path.join(self.temp_dir, 'test.pdf')
        with open(test_file_path, 'w') as f:
            f.write("test content")
        
        mock_db.get_attachment_by_id.return_value = {
            'filename': 'test.pdf',
            'file_path': test_file_path,
            'content_type': 'application/pdf',
            'file_size': 12,
            'classification_reason': 'Document file'
        }
        
        result = get_file_attachment.invoke({'attachment_id': 'abc123'})
        
        self.assertIn('Anhang gefunden', result)
        self.assertIn('test.pdf', result)
        self.assertIn(test_file_path, result)
    
    @patch('agent.tools.EmailDatabase')
    def test_get_file_attachment_not_found(self, mock_db_class):
        """Test file attachment not found."""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_attachment_by_id.return_value = None
        
        result = get_file_attachment.invoke({'attachment_id': 'nonexistent'})
        
        self.assertIn('wurde nicht gefunden', result)
    
    @patch('agent.tools.EmailDatabase')
    @patch('agent.tools.sqlite3')
    def test_list_email_attachments(self, mock_sqlite, mock_db_class):
        """Test listing email attachments."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite.connect.return_value.__enter__.return_value = mock_conn
        
        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db
        
        # Mock query results
        mock_cursor.fetchall.return_value = [
            ('abc123', 'test.pdf', 'application/pdf', 1024, 'email_123'),
            ('def456', 'document.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 2048, 'email_456')
        ]
        
        result = list_email_attachments.invoke({})
        
        self.assertIn('Verfügbare E-Mail-Anhänge', result)
        self.assertIn('test.pdf', result)
        self.assertIn('document.docx', result)
        self.assertIn('abc123', result)
        self.assertIn('def456', result)
    
    @patch('agent.tools.EmailDatabase')
    @patch('agent.tools.sqlite3')
    def test_list_email_attachments_empty(self, mock_sqlite, mock_db_class):
        """Test listing when no attachments available."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite.connect.return_value.__enter__.return_value = mock_conn
        
        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db
        
        # Mock empty results
        mock_cursor.fetchall.return_value = []
        
        result = list_email_attachments.invoke({})
        
        self.assertIn('Keine E-Mail-Anhänge verfügbar', result)

    @patch('email_agent.database.EmailDatabase')
    @patch('sqlite3.connect')
    @patch('agent.tools.ChatOpenAI')
    def test_process_file_with_openai_image(self, mock_openai, mock_sqlite_connect, mock_db_class):
        """Test processing an image file with OpenAI."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite_connect.return_value.__enter__.return_value = mock_conn

        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db

        # Create test image file
        test_image_path = os.path.join(self.temp_dir, 'test_image.png')
        with open(test_image_path, 'wb') as f:
            # Write minimal PNG header
            f.write(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde')

        # Mock query results
        mock_cursor.fetchone.return_value = (
            'test_image.png', 'image/png', test_image_path, 1024
        )

        # Mock OpenAI response
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.content = "Dies ist ein Test-Bild mit rotem Hintergrund."
        mock_llm.invoke.return_value = mock_response
        mock_openai.return_value = mock_llm

        # Mock os.path.exists
        with patch('agent.tools.os.path.exists', return_value=True):
            with patch('agent.tools.open', mock_open_image_file()):
                with patch('agent.tools.base64.b64encode', return_value=b'fake_base64_data'):
                    result = process_file_with_llm.invoke({
                        'attachment_id': 'abc123',
                        'prompt': 'Was siehst du in diesem Bild?'
                    })

        self.assertIn('Analyse von', result)
        self.assertIn('test_image.png', result)
        self.assertIn('Test-Bild', result)

    @patch('email_agent.database.EmailDatabase')
    @patch('sqlite3.connect')
    def test_process_file_with_openai_not_found(self, mock_sqlite_connect, mock_db_class):
        """Test processing a file that doesn't exist."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite_connect.return_value.__enter__.return_value = mock_conn

        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db

        # Mock empty query results
        mock_cursor.fetchone.return_value = None

        result = process_file_with_llm.invoke({
            'attachment_id': 'nonexistent',
            'prompt': 'Test prompt'
        })

        self.assertIn('nicht gefunden', result)

    @patch('email_agent.database.EmailDatabase')
    @patch('sqlite3.connect')
    def test_process_file_with_openai_unsupported_type(self, mock_sqlite_connect, mock_db_class):
        """Test processing an unsupported file type."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_sqlite_connect.return_value.__enter__.return_value = mock_conn

        # Mock database instance
        mock_db = Mock()
        mock_db.db_path = self.db_path
        mock_db_class.return_value = mock_db

        # Create test file
        test_file_path = os.path.join(self.temp_dir, 'test.txt')
        with open(test_file_path, 'w') as f:
            f.write("test content")

        # Mock query results with unsupported type
        mock_cursor.fetchone.return_value = (
            'test.txt', 'text/plain', test_file_path, 12
        )

        # Mock os.path.exists
        with patch('agent.tools.os.path.exists', return_value=True):
            result = process_file_with_llm.invoke({
                'attachment_id': 'abc123',
                'prompt': 'Test prompt'
            })

        self.assertIn('wird nicht unterstützt', result)
        self.assertIn('text/plain', result)


def mock_open_image_file():
    """Mock function for opening image files."""
    from unittest.mock import mock_open
    return mock_open(read_data=b'fake_image_data')


if __name__ == '__main__':
    unittest.main()
